/* Enhanced Module Search and Filter Styles */
.filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    background-color: var(--card-color);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.filter-container:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Enhanced Search Box */
.search-box {
    position: relative;
    flex: 1;
    max-width: 350px;
    transition: all 0.3s ease;
}

.search-box.searching {
    max-width: 100%;
}

.search-box input {
    width: 100%;
    padding: 0.8rem 1rem 0.8rem 3rem;
    border: 2px solid transparent;
    border-radius: 30px;
    font-size: 0.95rem;
    background-color: #f5f5f5;
    transition: all 0.3s ease;
    color: var(--text-color);
}

.search-box input:focus {
    outline: none;
    border-color: var(--highlight-color);
    background-color: #fff;
    box-shadow: 0 0 0 4px rgba(232, 135, 30, 0.1);
}

.search-box.has-value input {
    padding-right: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.search-box input:focus + .search-icon {
    color: var(--highlight-color);
}

.search-box .clear-search {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    font-size: 0.9rem;
    opacity: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #f0f0f0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-box.has-value .clear-search {
    opacity: 0.7;
}

.search-box.has-value .clear-search:hover {
    opacity: 1;
    color: var(--highlight-color);
    background-color: #e0e0e0;
}

/* Enhanced Sort Options */
.sort-options {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    position: relative;
}

.sort-options label {
    font-weight: 500;
    color: var(--primary-color);
    white-space: nowrap;
}

.sort-options select {
    padding: 0.7rem 2.5rem 0.7rem 1rem;
    border: 2px solid transparent;
    border-radius: 8px;
    background-color: #f5f5f5;
    color: var(--text-color);
    font-size: 0.95rem;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    transition: all 0.3s ease;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23A67B5B'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 20px;
}

.sort-options select:focus {
    outline: none;
    border-color: var(--highlight-color);
    background-color: #fff;
    box-shadow: 0 0 0 4px rgba(232, 135, 30, 0.1);
}

.sort-options select:hover {
    background-color: #f0f0f0;
}

/* No Results Message */
.no-search-results {
    text-align: center;
    padding: 2rem;
    background-color: rgba(101, 53, 15, 0.05);
    border-radius: 8px;
    margin-top: 1rem;
    color: var(--secondary-color);
    font-style: italic;
    animation: fadeIn 0.5s ease;
}

.no-search-results p {
    margin-bottom: 0.5rem;
}

.no-search-results .suggestion {
    font-size: 0.9rem;
    opacity: 0.8;
}

.no-search-results a {
    color: var(--highlight-color);
    text-decoration: none;
    font-weight: 500;
}

.no-search-results a:hover {
    text-decoration: underline;
}

/* Search Mode Styling */
.page-header h2.searching::before {
    content: 'Search Results';
    display: inline-block;
    margin-right: 10px;
    font-weight: 500;
    color: var(--highlight-color);
}

.page-header h2.searching {
    display: flex;
    align-items: center;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Search Results Count */
.search-results-count {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-left: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
    position: absolute;
    left: 0;
    top: -25px;
    background-color: var(--highlight-color);
    color: white;
    padding: 3px 10px;
    border-radius: 15px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transform: translateY(5px);
}

.search-results-count.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        padding: 1rem;
    }

    .search-box {
        max-width: 100%;
    }

    .sort-options {
        width: 100%;
    }

    .sort-options select {
        width: 100%;
    }
}
