<?php
/**
 * AI API Configuration
 *
 * This file contains configuration settings for the DeepSeek API integration.
 */

// DeepSeek API Key - Replace with your actual API key
// For security, consider using environment variables or a secure vault in production
define('AI_API_KEY', 'sk-2a39b6d9b691406db48661e792ffdc25');

// API Endpoint
define('AI_API_ENDPOINT', 'https://api.deepseek.com/v1');

// Model Configuration
define('AI_MODEL', 'deepseek-chat'); // Using DeepSeek Chat model

// Default parameters
define('DEFAULT_MAX_TOKENS', 1000);
define('DEFAULT_TEMPERATURE', 0.7);
define('DEFAULT_TOP_P', 1.0);
define('DEFAULT_FREQUENCY_PENALTY', 0.0);
define('DEFAULT_PRESENCE_PENALTY', 0.0);

/**
 * Get default parameters for DeepSeek API calls
 *
 * @return array Default parameters
 */
function getDefaultAIParams() {
    return [
        'model' => AI_MODEL,
        'max_tokens' => DEFAULT_MAX_TOKENS,
        'temperature' => DEFAULT_TEMPERATURE,
        'top_p' => DEFAULT_TOP_P,
        'frequency_penalty' => DEFAULT_FREQUENCY_PENALTY,
        'presence_penalty' => DEFAULT_PRESENCE_PENALTY
    ];
}
