<?php
session_start();
require_once('db_connection.php');
require_once('includes/functions.php');
require_once('includes/ai_functions.php');

// Check if user is logged in
if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Check if site is in maintenance mode
check_maintenance_mode($conn);

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Check if AI features are enabled
try {
    $stmt = $conn->prepare("SELECT setting_value FROM ai_settings WHERE setting_name = 'enable_ai_features'");
    $stmt->execute();
    $ai_enabled = $stmt->fetchColumn();
    
    if (!$ai_enabled) {
        header('Location: dashboard.php?error=AI features are currently disabled');
        exit;
    }
} catch (PDOException $e) {
    // Default to enabled if setting doesn't exist
    $ai_enabled = true;
}

// Get all modules for this user (for dropdown)
$stmt = $conn->prepare("SELECT * FROM modules WHERE user_id = ? ORDER BY module_name ASC");
$stmt->execute([$user_id]);
$modules = $stmt->fetchAll();

// Get notes for selected module
$module_id = isset($_GET['module']) ? intval($_GET['module']) : null;
$notes = [];

if ($module_id) {
    $stmt = $conn->prepare("
        SELECT n.*, m.module_name
        FROM notes n
        JOIN modules m ON n.module_id = m.module_id
        WHERE n.user_id = ? AND n.module_id = ?
        ORDER BY n.title ASC
    ");
    $stmt->execute([$user_id, $module_id]);
    $notes = $stmt->fetchAll();
}

// Get existing summaries
$stmt = $conn->prepare("
    SELECT s.*, n.title as note_title, m.module_name
    FROM ai_summaries s
    JOIN notes n ON s.note_id = n.note_id
    JOIN modules m ON n.module_id = m.module_id
    WHERE n.user_id = ?
    ORDER BY s.created_at DESC
");
$stmt->execute([$user_id]);
$summaries = $stmt->fetchAll();

// Handle summary generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'generate_summary') {
    $note_id = isset($_POST['note_id']) ? intval($_POST['note_id']) : null;
    
    // Validate input
    if (empty($note_id)) {
        $error = "Note is required";
    } else {
        // Generate summary
        $summary = generateNoteSummary($conn, $note_id, $user_id);
        
        if ($summary) {
            $success = "Summary generated successfully";
        } else {
            $error = "Failed to generate summary. Please try again.";
        }
    }
}

// Handle summary deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_summary') {
    $summary_id = isset($_POST['summary_id']) ? intval($_POST['summary_id']) : null;
    
    if ($summary_id) {
        try {
            // Get note_id to verify ownership
            $stmt = $conn->prepare("
                SELECT s.note_id
                FROM ai_summaries s
                JOIN notes n ON s.note_id = n.note_id
                WHERE s.summary_id = ? AND n.user_id = ?
            ");
            $stmt->execute([$summary_id, $user_id]);
            $note_id = $stmt->fetchColumn();
            
            if ($note_id) {
                // Delete summary
                $stmt = $conn->prepare("DELETE FROM ai_summaries WHERE summary_id = ?");
                $stmt->execute([$summary_id]);
                
                $success = "Summary deleted successfully";
            } else {
                $error = "Summary not found or you don't have permission to delete it";
            }
        } catch (PDOException $e) {
            $error = "Error deleting summary: " . $e->getMessage();
        }
    }
}

// View specific summary
$view_summary = null;
if (isset($_GET['view']) && !empty($_GET['view'])) {
    $summary_id = intval($_GET['view']);
    
    $stmt = $conn->prepare("
        SELECT s.*, n.title as note_title, n.content as note_content, m.module_name
        FROM ai_summaries s
        JOIN notes n ON s.note_id = n.note_id
        JOIN modules m ON n.module_id = m.module_id
        WHERE s.summary_id = ? AND n.user_id = ?
    ");
    $stmt->execute([$summary_id, $user_id]);
    $view_summary = $stmt->fetch();
    
    if (!$view_summary) {
        header('Location: ai_summary.php?error=Summary not found');
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - AI Summary Generator</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/user.css">
    <link rel="stylesheet" href="css/form-elements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .summary-card {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            padding: 15px;
            margin-bottom: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid var(--accent-color);
        }
        
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .summary-title {
            font-size: 18px;
            color: var(--primary-color);
            margin: 0;
        }
        
        .summary-meta {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: var(--secondary-color);
        }
        
        .summary-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .ai-icon {
            color: var(--highlight-color);
            margin-right: 5px;
        }
        
        .note-selector {
            display: none;
        }
        
        .note-selector.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        
        .summary-container {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .summary-content {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            line-height: 1.6;
        }
        
        .original-note {
            background-color: #f0f0f0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            border-left: 4px solid var(--primary-color);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Student Dashboard</p>
            </div>
            <div class="user-profile">
                <span class="user-name"><i class="fas fa-user-graduate"></i> <?php echo htmlspecialchars($username); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        <a href="modules.php"><i class="fas fa-book"></i> Modules</a>
                        <a href="notes.php"><i class="fas fa-sticky-note"></i> Notes</a>
                        <a href="ai_quiz.php"><i class="fas fa-robot"></i> AI Quiz</a>
                        <a href="ai_summary.php" class="active"><i class="fas fa-robot"></i> AI Summary</a>
                        <a href="ai_chat.php"><i class="fas fa-robot"></i> AI Chat</a>
                        <a href="profile.php"><i class="fas fa-user"></i> Profile</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2><i class="fas fa-robot ai-icon"></i> AI Summary Generator</h2>
                        <?php if (!$view_summary): ?>
                            <button id="generateSummaryBtn" class="btn"><i class="fas fa-plus"></i> Generate New Summary</button>
                        <?php else: ?>
                            <a href="ai_summary.php" class="btn"><i class="fas fa-arrow-left"></i> Back to Summaries</a>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($error)): ?>
                        <div class="error-message">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($success)): ?>
                        <div class="success-message">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($view_summary): ?>
                        <!-- View Single Summary -->
                        <div class="summary-container">
                            <h1 class="summary-title">Summary: <?php echo htmlspecialchars($view_summary['note_title']); ?></h1>
                            
                            <div class="summary-meta">
                                <div>
                                    <span><i class="fas fa-book"></i> <?php echo htmlspecialchars($view_summary['module_name']); ?></span>
                                </div>
                                <div>
                                    <span><i class="fas fa-calendar-alt"></i> <?php echo date('M d, Y', strtotime($view_summary['created_at'])); ?></span>
                                </div>
                            </div>
                            
                            <div class="summary-content">
                                <?php echo nl2br(htmlspecialchars($view_summary['summary_content'])); ?>
                            </div>
                            
                            <div class="summary-actions">
                                <a href="ai_summary.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Summaries</a>
                                <button class="btn btn-danger delete-summary-btn" data-id="<?php echo $view_summary['summary_id']; ?>" data-title="<?php echo htmlspecialchars($view_summary['note_title']); ?>">
                                    <i class="fas fa-trash"></i> Delete Summary
                                </button>
                            </div>
                            
                            <h3>Original Note</h3>
                            <div class="original-note">
                                <?php echo $view_summary['note_content']; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Summary List -->
                        <div class="card">
                            <div class="card-header">
                                <h3>Your Summaries</h3>
                            </div>
                            <div class="card-body">
                                <?php if (count($summaries) > 0): ?>
                                    <?php foreach ($summaries as $summary): ?>
                                        <div class="summary-card">
                                            <div class="summary-header">
                                                <h4 class="summary-title"><?php echo htmlspecialchars($summary['note_title']); ?></h4>
                                            </div>
                                            <div class="summary-meta">
                                                <span><i class="fas fa-book"></i> <?php echo htmlspecialchars($summary['module_name']); ?></span>
                                                <span><i class="fas fa-calendar-alt"></i> <?php echo date('M d, Y', strtotime($summary['created_at'])); ?></span>
                                            </div>
                                            <div class="summary-actions">
                                                <a href="ai_summary.php?view=<?php echo $summary['summary_id']; ?>" class="btn-sm"><i class="fas fa-eye"></i> View</a>
                                                <button class="btn-sm btn-danger delete-summary-btn" data-id="<?php echo $summary['summary_id']; ?>" data-title="<?php echo htmlspecialchars($summary['note_title']); ?>">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p class="no-data">You haven't generated any summaries yet. Click "Generate New Summary" to create your first summary.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <!-- Generate Summary Modal -->
    <div id="generateSummaryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-robot ai-icon"></i> Generate AI Summary</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form action="ai_summary.php" method="post">
                    <input type="hidden" name="action" value="generate_summary">
                    
                    <div class="form-group">
                        <label for="module_id">Select Module</label>
                        <select id="module_id" name="module_id" required>
                            <option value="">-- Select Module --</option>
                            <?php foreach ($modules as $module): ?>
                                <option value="<?php echo $module['module_id']; ?>" <?php echo ($module_id == $module['module_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($module['module_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div id="noteSelector" class="form-group note-selector <?php echo $module_id ? 'active' : ''; ?>">
                        <label for="note_id">Select Note</label>
                        <select id="note_id" name="note_id" required>
                            <option value="">-- Select Note --</option>
                            <?php foreach ($notes as $note): ?>
                                <option value="<?php echo $note['note_id']; ?>">
                                    <?php echo htmlspecialchars($note['title']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn"><i class="fas fa-robot"></i> Generate Summary</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Summary Modal -->
    <div id="deleteSummaryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-trash"></i> Delete Summary</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the summary for "<span id="deleteSummaryTitle"></span>"?</p>
                <p>This action cannot be undone.</p>
                
                <form action="ai_summary.php" method="post">
                    <input type="hidden" name="action" value="delete_summary">
                    <input type="hidden" id="deleteSummaryId" name="summary_id">
                    
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Summary</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/user.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Generate Summary Button
            const generateSummaryBtn = document.getElementById('generateSummaryBtn');
            const generateSummaryModal = document.getElementById('generateSummaryModal');
            
            if (generateSummaryBtn && generateSummaryModal) {
                generateSummaryBtn.addEventListener('click', function() {
                    openModalWithAnimation(generateSummaryModal);
                });
            }
            
            // Module selection changes note options
            const moduleSelect = document.getElementById('module_id');
            const noteSelector = document.getElementById('noteSelector');
            
            if (moduleSelect) {
                moduleSelect.addEventListener('change', function() {
                    const moduleId = this.value;
                    if (moduleId) {
                        window.location.href = 'ai_summary.php?module=' + moduleId;
                    }
                });
            }
            
            // Delete Summary Button
            const deleteSummaryBtns = document.querySelectorAll('.delete-summary-btn');
            const deleteSummaryModal = document.getElementById('deleteSummaryModal');
            const deleteSummaryTitle = document.getElementById('deleteSummaryTitle');
            const deleteSummaryId = document.getElementById('deleteSummaryId');
            
            deleteSummaryBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const summaryId = this.getAttribute('data-id');
                    const summaryTitle = this.getAttribute('data-title');
                    
                    if (deleteSummaryTitle) deleteSummaryTitle.textContent = summaryTitle;
                    if (deleteSummaryId) deleteSummaryId.value = summaryId;
                    
                    if (deleteSummaryModal) {
                        openModalWithAnimation(deleteSummaryModal);
                    }
                });
            });
        });
    </script>
</body>
</html>
