
/* Quiz Styles */

/* Quiz Card */
.quizzes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.quiz-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    flex-direction: column;
}

.quiz-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.quiz-header {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.quiz-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
    flex: 1;
}

.module-badge {
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    margin-left: 10px;
}

.quiz-info {
    color: var(--secondary-color);
    font-size: 14px;
    margin-bottom: 15px;
    flex-grow: 1;
}

.quiz-info p {
    margin: 5px 0;
}

.quiz-info i {
    width: 20px;
    text-align: center;
    margin-right: 5px;
    color: var(--highlight-color);
}

.quiz-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

/* Quiz Container */
.quiz-container {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.quiz-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Relationship Card */
.note-quiz-relationship {
    margin-bottom: 25px;
}

.relationship-card {
    display: flex;
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.relationship-icon {
    font-size: 24px;
    color: var(--primary-color);
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: rgba(101, 53, 15, 0.1);
    border-radius: 50%;
}

.relationship-content {
    flex: 1;
}

.relationship-content h4 {
    margin-top: 0;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.relationship-content p {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.quiz-meta {
    display: flex;
    align-items: center;
    gap: 15px;
}

.question-count {
    color: var(--secondary-color);
    font-size: 14px;
}

.quiz-score {
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
}

.high-score {
    background-color: #d4edda;
    color: #155724;
}

.medium-score {
    background-color: #fff3cd;
    color: #856404;
}

.low-score {
    background-color: #f8d7da;
    color: #721c24;
}

/* Question Card */
.question-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid var(--accent-color);
}

.question-card.correct {
    border-left-color: #28a745;
}

.question-card.incorrect {
    border-left-color: #dc3545;
}

.question-number {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 16px;
}

.question-content-container {
    margin-bottom: 20px;
}

.question-text {
    font-size: 18px;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
    transition: max-height 0.3s ease;
}

.question-text.expanded {
    max-height: 800px;
}

.question-expand-collapse {
    text-align: center;
    margin-top: 10px;
}

.btn-accent {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

.btn-accent:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
}

.answer-input {
    margin-bottom: 10px;
}

.answer-input label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--secondary-color);
}

.answer-input input {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 5px;
    font-size: 16px;
}

.answer-input input:focus {
    border-color: var(--highlight-color);
    outline: none;
}

/* Multiple-choice options */
.answer-options {
    margin-bottom: 15px;
}

.options-label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--secondary-color);
}

.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    transition: background-color 0.2s, border-color 0.2s;
}

.option-item:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
}

.option-item input[type="radio"] {
    margin-right: 10px;
}

.option-item label {
    flex: 1;
    cursor: pointer;
    font-size: 16px;
    margin: 0;
}

/* Multiple-choice results */
.options-results {
    margin-bottom: 15px;
}

.option-result {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.option-marker {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.option-text {
    flex: 1;
}

.correct-option {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 3px solid #28a745;
}

.incorrect-option {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 3px solid #dc3545;
}

/* Quiz Results */
.quiz-results {
    margin-top: 20px;
}

.results-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.results-header h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.results-summary {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.result-item {
    background-color: #f9f9f9;
    padding: 10px 15px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.result-label {
    font-weight: 500;
    color: var(--secondary-color);
}

.result-value {
    font-weight: 600;
    color: var(--primary-color);
}

.answer-section {
    background-color: #fff;
    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
}

.user-answer {
    margin-bottom: 10px;
}

.answer-label {
    font-weight: 500;
    color: var(--secondary-color);
    margin-right: 10px;
}

.feedback-message {
    font-weight: 500;
}

.question-card.correct .feedback-message {
    color: #28a745;
}

.question-card.incorrect .feedback-message {
    color: #dc3545;
}

/* Quiz Actions */
.quiz-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
}

/* Breadcrumb Navigation */
.breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
}

.breadcrumb a {
    color: var(--secondary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: var(--primary-color);
}

.breadcrumb .separator {
    margin: 0 8px;
    color: var(--secondary-color);
}

.breadcrumb .current {
    color: var(--primary-color);
    font-weight: 500;
}

/* Page Actions */
.page-actions {
    display: flex;
    gap: 10px;
}

/* Slim buttons - Using standardized style from style.css */

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--accent-color);
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

/* Generation Progress */
.generation-progress {
    text-align: center;
    padding: 30px 20px;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--highlight-color);
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.generation-progress p {
    margin-bottom: 5px;
    color: var(--primary-color);
    font-weight: 500;
}

.generation-progress p.small {
    font-size: 14px;
    color: var(--secondary-color);
}

/* Radio Group */
.radio-group {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.radio-label input[type="radio"] {
    margin-right: 5px;
}

