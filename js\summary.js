/**
 * Summary functionality for StudyNotes
 */
document.addEventListener('DOMContentLoaded', function() {
    // Setup generate summary modal
    const generateSummaryBtn = document.getElementById('generateSummaryBtn');
    const generateSummaryModal = document.getElementById('generateSummaryModal');
    const generateSummaryForm = document.getElementById('generateSummaryForm');
    const generationProgress = document.getElementById('generationProgress');

    // Setup delete summary modal
    const deleteSummaryBtns = document.querySelectorAll('.delete-summary-btn');
    const deleteSummaryModal = document.getElementById('deleteSummaryModal');

    // Setup regenerate button
    const regenerateBtn = document.getElementById('regenerateBtn');
    const regenerationModal = document.getElementById('regenerationModal');

    // Open generate summary modal
    if (generateSummaryBtn && generateSummaryModal) {
        generateSummaryBtn.addEventListener('click', function() {
            openModal(generateSummaryModal);
        });
    }

    // Handle summary generation form submission
    if (generateSummaryForm) {
        generateSummaryForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show progress indicator
            if (generationProgress) {
                generateSummaryForm.style.display = 'none';
                generationProgress.style.display = 'block';
            }
            
            // Get form data
            const formData = {
                note_id: document.getElementById('note_id').value,
                max_length: document.getElementById('max_length').value
            };
            
            // Send API request
            fetch('api/generate_summary.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect to view summary page
                    window.location.href = `view_summary.php?id=${data.summary_id}&success=Summary generated successfully`;
                } else {
                    // Show error
                    alert('Error: ' + (data.error || 'Failed to generate summary'));
                    
                    // Hide progress indicator
                    if (generationProgress) {
                        generateSummaryForm.style.display = 'block';
                        generationProgress.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while generating the summary. Please try again.');
                
                // Hide progress indicator
                if (generationProgress) {
                    generateSummaryForm.style.display = 'block';
                    generationProgress.style.display = 'none';
                }
            });
        });
    }

    // Setup delete summary buttons
    if (deleteSummaryBtns.length > 0 && deleteSummaryModal) {
        deleteSummaryBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const summaryId = this.getAttribute('data-id');
                const summaryTitle = this.getAttribute('data-title');
                
                document.getElementById('delete_summary_id').value = summaryId;
                document.getElementById('delete_summary_title').textContent = summaryTitle;
                
                openModal(deleteSummaryModal);
            });
        });
    }

    // Handle regenerate button
    if (regenerateBtn && regenerationModal) {
        regenerateBtn.addEventListener('click', function() {
            const noteId = this.getAttribute('data-note-id');
            const summaryId = this.getAttribute('data-summary-id');
            
            // Show regeneration modal
            openModal(regenerationModal);
            
            // Send API request
            fetch('api/generate_summary.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    note_id: noteId,
                    max_length: 500 // Default length
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show the new summary
                    window.location.reload();
                } else {
                    // Show error
                    closeModal(regenerationModal);
                    alert('Error: ' + (data.error || 'Failed to regenerate summary'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                closeModal(regenerationModal);
                alert('An error occurred while regenerating the summary. Please try again.');
            });
        });
    }

    // Setup summary form validation
    if (generateSummaryForm) {
        const maxLengthInput = document.getElementById('max_length');
        
        if (maxLengthInput) {
            maxLengthInput.addEventListener('input', function() {
                const value = parseInt(this.value);
                if (value < 100) this.value = 100;
                if (value > 2000) this.value = 2000;
            });
        }
    }

    // Close modals
    const closeBtns = document.querySelectorAll('.close, .cancel-btn');
    closeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                closeModal(modal);
                
                // Reset form if it's the generate summary modal
                if (modal.id === 'generateSummaryModal' && generateSummaryForm && generationProgress) {
                    generateSummaryForm.style.display = 'block';
                    generationProgress.style.display = 'none';
                }
            }
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
            
            // Reset form if it's the generate summary modal
            if (e.target.id === 'generateSummaryModal' && generateSummaryForm && generationProgress) {
                generateSummaryForm.style.display = 'block';
                generationProgress.style.display = 'none';
            }
        }
    });
});
