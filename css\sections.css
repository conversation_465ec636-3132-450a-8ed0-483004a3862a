/* 
 * Consistent Section Sizing for StudyNotes Application
 * This file standardizes the dimensions, padding, and margins for all section containers
 */

/* Standard Section Container */
.section-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto 30px;
    padding: 20px;
    background-color: var(--card-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: -20px -20px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 8px 8px 0 0;
    background-color: rgba(101, 53, 15, 0.03);
}

.section-header h2, 
.section-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 600;
}

/* Section Body */
.section-body {
    padding: 0;
    min-height: 200px;
}

/* Section Footer */
.section-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 20px -20px -20px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 0 0 8px 8px;
    background-color: rgba(101, 53, 15, 0.02);
}

/* Grid Layouts for Content */
.section-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    width: 100%;
}

/* Card Items within Sections */
.section-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid var(--accent-color);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.section-card-header {
    margin-bottom: 10px;
}

.section-card-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
}

.section-card-body {
    flex: 1;
    margin-bottom: 15px;
    overflow: hidden;
}

.section-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Standardized Sidebar */
.standard-sidebar {
    width: 250px;
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 20px 0;
    height: fit-content;
}

/* Standardized Content Area */
.standard-content {
    flex: 1;
    min-width: 0; /* Prevents flex items from overflowing */
}

/* Consistent Dashboard Layout */
.standard-dashboard {
    display: flex;
    gap: 20px;
    min-height: calc(100vh - 180px);
}

/* Consistent Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    height: 100px;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .standard-dashboard {
        flex-direction: column;
    }
    
    .standard-sidebar {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .section-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 576px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .section-footer {
        flex-direction: column;
        gap: 10px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-item {
        height: 80px;
    }
}
