/* Dashboard Styles */
:root {
    --primary-color: #65350F;       /* <PERSON> Brown */
    --secondary-color: #A67B5B;     /* Medium Brown */
    --accent-color: #D4A373;        /* <PERSON> Brown */
    --highlight-color: #E8871E;     /* Orange */
    --text-color: #2D2424;          /* Dark Brown Text */
    --light-text: #F5EBE0;          /* Light Text */
    --background-color: #F5EBE0;    /* Light Beige */
    --card-color: #FFFFFF;          /* White */
    --danger-color: #dc3545;        /* Red for delete actions */
    --success-color: #28a745;       /* Green for success messages */
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: calc(100vh - 180px);
}

.sidebar {
    width: 250px;
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 20px 0;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
}

.sidebar-menu a {
    color: var(--light-text);
    text-decoration: none;
    padding: 12px 20px;
    display: flex;
    align-items: center;
}

.sidebar-menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu a.active {
    background-color: var(--highlight-color);
    border-left: 4px solid var(--light-text);
}

.content {
    flex: 1;
    padding: 20px;
    background-color: var(--background-color);
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    color: var(--primary-color);
    margin: 0;
}

/* User Profile */
.user-profile {
    display: flex;
    align-items: center;
}

.user-name {
    margin-right: 15px;
    font-weight: 500;
}

.logout-btn {
    color: var(--light-text);
    text-decoration: none;
    background-color: var(--highlight-color);
    padding: 5px 10px;
    border-radius: 4px;
}

.logout-btn:hover {
    background-color: var(--primary-color);
}

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.stat-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.stat-info h3 {
    font-size: 28px;
    margin: 0 0 5px 0;
    color: var(--primary-color);
}

.stat-info p {
    margin: 0;
    color: var(--secondary-color);
}

/* Cards */
.card {
    background-color: var(--card-color);
    border-radius: 8px;
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.view-all {
    color: var(--highlight-color);
    text-decoration: none;
    font-weight: 500;
}

.view-all:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.card-body {
    padding: 20px;
}

/* Notes Grid */
.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.note-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid var(--accent-color);
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.note-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
}

.module-badge {
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.note-preview {
    color: var(--text-color);
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
    height: 60px;
    overflow: hidden;
}

.note-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.note-date {
    color: var(--secondary-color);
}

.btn-sm {
    background-color: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    text-decoration: none;
}

.btn-sm:hover {
    background-color: var(--highlight-color);
    color: var(--light-text);
}

/* Modules Grid */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.module-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    border-top: 4px solid var(--primary-color);
}

.module-header {
    margin-bottom: 10px;
}

.module-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
}

.module-description {
    color: var(--text-color);
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
    height: 60px;
    overflow: hidden;
}

.module-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-actions {
    display: flex;
    gap: 5px;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
}

.btn-icon:hover {
    color: var(--primary-color);
}

.btn-icon.delete-module-btn:hover {
    color: var(--danger-color);
}

/* No Data Message */
.no-data {
    text-align: center;
    padding: 30px;
    color: var(--secondary-color);
}

.no-data a {
    color: var(--highlight-color);
    text-decoration: none;
    font-weight: 500;
}

.no-data a:hover {
    text-decoration: underline;
}

/* Success and Error Messages */
.success-message, .error-message {
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.success-message {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    color: var(--success-color);
}

.error-message {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.modal-content {
    background-color: var(--card-color);
    margin: 10% auto;
    width: 90%;
    max-width: 500px;
    border-radius: 8px;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.close {
    color: var(--secondary-color);
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
    font-weight: 500;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.form-group input:focus, .form-group textarea:focus {
    border-color: var(--accent-color);
    outline: none;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    background-color: var(--primary-color);
    color: var(--light-text);
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.btn:hover {
    background-color: var(--highlight-color);
}

.btn-secondary {
    background-color: #f0f0f0;
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #e0e0e0;
    color: var(--text-color);
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Slim buttons - Using standardized style from style.css */

.warning {
    color: var(--danger-color);
    font-size: 14px;
    margin-top: 5px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 10px 0;
    }

    .sidebar-menu {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 10px;
    }

    .sidebar-menu a {
        padding: 10px 15px;
        white-space: nowrap;
    }

    .sidebar-menu a.active {
        border-left: none;
        border-bottom: 4px solid var(--light-text);
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .notes-grid, .modules-grid {
        grid-template-columns: 1fr;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
}
