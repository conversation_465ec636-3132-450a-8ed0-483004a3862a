/* Dashboard Styles - CSS variables moved to common.css */

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: calc(100vh - 180px);
}

.sidebar {
    width: 250px;
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 20px 0;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
}

.sidebar-menu a {
    color: var(--light-text);
    text-decoration: none;
    padding: 12px 20px;
    display: flex;
    align-items: center;
}

.sidebar-menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu a.active {
    background-color: var(--highlight-color);
    border-left: 4px solid var(--light-text);
}

.content {
    flex: 1;
    padding: 20px;
    background-color: var(--background-color);
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    color: var(--primary-color);
    margin: 0;
}

/* User Profile */
.user-profile {
    display: flex;
    align-items: center;
}

.user-name {
    margin-right: 15px;
    font-weight: 500;
}

.logout-btn {
    color: var(--light-text);
    text-decoration: none;
    background-color: var(--highlight-color);
    padding: 5px 10px;
    border-radius: 4px;
}

.logout-btn:hover {
    background-color: var(--primary-color);
}

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.stat-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.stat-info h3 {
    font-size: 28px;
    margin: 0 0 5px 0;
    color: var(--primary-color);
}

.stat-info p {
    margin: 0;
    color: var(--secondary-color);
}

/* Cards */
.card {
    background-color: var(--card-color);
    border-radius: 8px;
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.view-all {
    color: var(--highlight-color);
    text-decoration: none;
    font-weight: 500;
}

.view-all:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.card-body {
    padding: 20px;
}

/* Dashboard-specific overrides - common styles moved to common.css */

/* Dashboard-specific note preview height override */
.note-preview {
    height: 60px;
    overflow: hidden;
}

/* Dashboard-specific module description height override */
.module-description {
    height: 60px;
    overflow: hidden;
}

/* No Data Message */
.no-data {
    text-align: center;
    padding: 30px;
    color: var(--secondary-color);
}

.no-data a {
    color: var(--highlight-color);
    text-decoration: none;
    font-weight: 500;
}

.no-data a:hover {
    text-decoration: underline;
}

/* Message styles moved to common.css */

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.modal-content {
    background-color: var(--card-color);
    margin: 10% auto;
    width: 90%;
    max-width: 500px;
    border-radius: 8px;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.close {
    color: var(--secondary-color);
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
    font-weight: 500;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.form-group input:focus, .form-group textarea:focus {
    border-color: var(--accent-color);
    outline: none;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    background-color: var(--primary-color);
    color: var(--light-text);
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.btn:hover {
    background-color: var(--highlight-color);
}

.btn-secondary {
    background-color: #f0f0f0;
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #e0e0e0;
    color: var(--text-color);
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Slim buttons - Using standardized style from style.css */

.warning {
    color: var(--danger-color);
    font-size: 14px;
    margin-top: 5px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 10px 0;
    }

    .sidebar-menu {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 10px;
    }

    .sidebar-menu a {
        padding: 10px 15px;
        white-space: nowrap;
    }

    .sidebar-menu a.active {
        border-left: none;
        border-bottom: 4px solid var(--light-text);
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
}
