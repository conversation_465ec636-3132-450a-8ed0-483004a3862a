<?php
session_start();
require_once('db_connection.php');
require_once('includes/functions.php');

// Check if user is logged in
if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Check if site is in maintenance mode
check_maintenance_mode($conn);

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Check if note ID is provided
if (!isset($_GET['id'])) {
    header('Location: notes.php?error=Note ID is required');
    exit;
}

$note_id = intval($_GET['id']);

// Get note details
$stmt = $conn->prepare("
    SELECT n.*, m.module_name
    FROM notes n
    JOIN modules m ON n.module_id = m.module_id
    WHERE n.note_id = ? AND n.user_id = ?
");
$stmt->execute([$note_id, $user_id]);
$note = $stmt->fetch();

// Check if note exists
if (!$note) {
    header('Location: notes.php?error=Note not found');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - <?php echo htmlspecialchars($note['title']); ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/user.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Additional styles for note view */
        .note-container {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .note-title {
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 28px;
        }

        .note-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            color: var(--secondary-color);
            font-size: 14px;
        }

        .note-module {
            background-color: var(--accent-color);
            color: var(--primary-color);
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: 500;
        }

        .note-dates span {
            margin-right: 15px;
        }

        .note-content {
            line-height: 1.8;
            color: var(--text-color);
            font-size: 16px;
        }

        .note-content h1, .note-content h2, .note-content h3 {
            color: var(--primary-color);
        }

        .note-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }

        .note-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }

        .note-content table, .note-content th, .note-content td {
            border: 1px solid #ddd;
        }

        .note-content th, .note-content td {
            padding: 8px 12px;
            text-align: left;
        }

        .note-content th {
            background-color: var(--accent-color);
            color: var(--primary-color);
        }

        .note-content blockquote {
            border-left: 4px solid var(--accent-color);
            padding-left: 15px;
            margin-left: 0;
            color: var(--secondary-color);
        }

        .note-content code {
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }

        .note-content pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }

        .note-actions {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }

        .print-btn {
            margin-left: auto;
        }

        @media print {
            .sidebar, .user-profile, .page-header, .note-actions, footer {
                display: none !important;
            }

            .dashboard-container {
                display: block !important;
            }

            .content {
                padding: 0 !important;
            }

            .note-container {
                box-shadow: none !important;
                padding: 0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">View Note</p>
            </div>
            <div class="user-profile">
                <span class="user-name"><i class="fas fa-user-graduate"></i> <?php echo htmlspecialchars($username); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        <a href="modules.php"><i class="fas fa-book"></i> Modules</a>
                        <a href="notes.php" class="active"><i class="fas fa-sticky-note"></i> Notes</a>
                        <a href="ai_quiz.php"><i class="fas fa-robot"></i> AI Quiz</a>
                        <a href="ai_summary.php"><i class="fas fa-robot"></i> AI Summary</a>
                        <a href="ai_chat.php"><i class="fas fa-robot"></i> AI Chat</a>
                        <a href="profile.php"><i class="fas fa-user"></i> Profile</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2>View Note</h2>
                        <a href="notes.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Notes</a>
                    </div>

                    <div class="note-container">
                        <h1 class="note-title"><?php echo htmlspecialchars($note['title']); ?></h1>

                        <div class="note-meta">
                            <div class="note-module">
                                <i class="fas fa-book"></i> <?php echo htmlspecialchars($note['module_name']); ?>
                            </div>
                            <div class="note-dates">
                                <span><i class="fas fa-calendar-plus"></i> Created: <?php echo date('M d, Y', strtotime($note['created_at'])); ?></span>
                                <span><i class="fas fa-calendar-check"></i> Updated: <?php echo date('M d, Y', strtotime($note['updated_at'])); ?></span>
                            </div>
                        </div>

                        <div class="note-content">
                            <?php echo $note['content']; ?>
                        </div>

                        <div class="note-actions">
                            <a href="notes.php?action=edit&id=<?php echo $note['note_id']; ?>" class="btn"><i class="fas fa-edit"></i> Edit Note</a>
                            <button class="btn btn-danger delete-note-btn" data-id="<?php echo $note['note_id']; ?>" data-title="<?php echo htmlspecialchars($note['title']); ?>">
                                <i class="fas fa-trash"></i> Delete Note
                            </button>
                            <button class="btn btn-secondary print-btn" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Note
                            </button>
                        </div>

                        <!-- AI Actions -->
                        <div class="note-actions" style="margin-top: 15px; border-top: 1px solid #eee; padding-top: 15px;">
                            <h4 style="margin-bottom: 10px;"><i class="fas fa-robot" style="color: var(--highlight-color);"></i> AI Tools</h4>
                            <a href="ai_quiz.php?module=<?php echo $note['module_id']; ?>" class="btn" style="background-color: var(--accent-color);">
                                <i class="fas fa-question-circle"></i> Generate Quiz
                            </a>
                            <form action="ai_summary.php" method="post" style="display: inline;">
                                <input type="hidden" name="action" value="generate_summary">
                                <input type="hidden" name="note_id" value="<?php echo $note['note_id']; ?>">
                                <button type="submit" class="btn" style="background-color: var(--accent-color);">
                                    <i class="fas fa-file-alt"></i> Generate Summary
                                </button>
                            </form>
                            <a href="ai_chat.php" class="btn" style="background-color: var(--accent-color);">
                                <i class="fas fa-comments"></i> Ask AI Assistant
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <!-- Delete Note Modal -->
    <div id="deleteNoteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Delete Note</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete note <strong id="delete_note_title"></strong>?</p>
                <p class="warning">This action cannot be undone.</p>
                <form action="notes.php" method="post">
                    <input type="hidden" name="action" value="delete_note">
                    <input type="hidden" name="note_id" id="delete_note_id">
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Note</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/user.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Setup delete note modal
            const deleteNoteBtn = document.querySelector('.delete-note-btn');
            const deleteNoteModal = document.getElementById('deleteNoteModal');

            if (deleteNoteBtn && deleteNoteModal) {
                deleteNoteBtn.addEventListener('click', function() {
                    const noteId = this.getAttribute('data-id');
                    const noteTitle = this.getAttribute('data-title');

                    document.getElementById('delete_note_id').value = noteId;
                    document.getElementById('delete_note_title').textContent = noteTitle;

                    openModalWithAnimation(deleteNoteModal);
                });
            }
        });
    </script>
</body>
</html>
