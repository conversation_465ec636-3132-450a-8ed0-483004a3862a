<?php
// Database connection script to add note_id column to quizzes table
require_once('db_connection.php');

try {
    // Check if the column already exists
    $stmt = $conn->prepare("SHOW COLUMNS FROM quizzes LIKE 'note_id'");
    $stmt->execute();
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        // Add the note_id column if it doesn't exist
        $sql = "ALTER TABLE quizzes ADD COLUMN note_id INT DEFAULT NULL";
        $conn->exec($sql);
        echo "Success: The note_id column has been added to the quizzes table.";
    } else {
        echo "Info: The note_id column already exists in the quizzes table.";
    }
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
