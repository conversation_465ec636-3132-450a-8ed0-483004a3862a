-- Update quiz_questions table to support multiple-choice options
ALTER TABLE quiz_questions 
ADD COLUMN question_type ENUM('multiple_choice', 'short_answer') NOT NULL DEFAULT 'multiple_choice' AFTER question_text,
ADD COLUMN difficulty ENUM('easy', 'medium', 'hard') NOT NULL DEFAULT 'medium' AFTER question_type,
ADD COLUMN options JSON NULL AFTER correct_answer;

-- Create table for AI chat history
CREATE TABLE IF NOT EXISTS ai_chat_history (
    chat_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    message_text TEXT NOT NULL,
    response_text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Create table for AI settings
CREATE TABLE IF NOT EXISTS ai_settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    setting_name <PERSON><PERSON>HA<PERSON>(50) NOT NULL UNIQUE,
    setting_value TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default AI settings
INSERT INTO ai_settings (setting_name, setting_value) VALUES 
('openai_api_key', ''),
('default_quiz_questions', '5'),
('default_quiz_difficulty', 'medium'),
('default_quiz_type', 'multiple_choice'),
('enable_ai_features', '1');
