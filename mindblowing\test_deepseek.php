<?php
/**
 * DeepSeek API Test Script
 * 
 * This script tests the DeepSeek API integration.
 */

require_once('includes/openai_config.php');
require_once('includes/openai_handler.php');

// Check if API key is set
if (AI_API_KEY === 'YOUR_DEEPSEEK_API_KEY_HERE') {
    echo "<p style='color: red;'>Error: Please set your DeepSeek API key in includes/openai_config.php first.</p>";
    exit;
}

// Initialize the handler
$ai = new OpenAIHandler();

// Test message
$messages = [
    ['role' => 'system', 'content' => 'You are a helpful assistant.'],
    ['role' => 'user', 'content' => 'Write a short paragraph about the importance of studying.']
];

// Make the API call
echo "<h2>Testing DeepSeek API</h2>";
echo "<p>Sending request to DeepSeek API...</p>";

$start_time = microtime(true);
$response = $ai->generateText($messages);
$end_time = microtime(true);

// Display results
echo "<h3>Results:</h3>";

if ($response) {
    echo "<p style='color: green;'>Success! API call completed in " . round($end_time - $start_time, 2) . " seconds.</p>";
    echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    echo "<h4>Response:</h4>";
    echo "<p>" . nl2br(htmlspecialchars($response)) . "</p>";
    echo "</div>";
} else {
    echo "<p style='color: red;'>Error: Failed to get a response from the DeepSeek API.</p>";
    echo "<p>Please check your API key and make sure it's valid.</p>";
}
?>
