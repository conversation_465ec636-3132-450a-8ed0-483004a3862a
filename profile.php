<?php
session_start();
require_once('db_connection.php');
require_once('includes/functions.php');

// Check if user is logged in
if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}



// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Get user stats
$stmt = $conn->prepare("SELECT COUNT(*) as note_count FROM notes WHERE user_id = ?");
$stmt->execute([$user_id]);
$note_count = $stmt->fetch()['note_count'];

$stmt = $conn->prepare("SELECT COUNT(*) as module_count FROM modules WHERE user_id = ?");
$stmt->execute([$user_id]);
$module_count = $stmt->fetch()['module_count'];

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'change_password') {
    $current_password = trim($_POST['current_password']);
    $new_password = trim($_POST['new_password']);
    $confirm_password = trim($_POST['confirm_password']);

    // Validate input
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error = "All fields are required";
    } elseif ($new_password !== $confirm_password) {
        $error = "New passwords do not match";
    } else {
        // Check current password
        $stmt = $conn->prepare("SELECT password FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();

        if ($user && $current_password === $user['password']) { // In a real app, use password_verify()
            // Update password
            try {
                $stmt = $conn->prepare("UPDATE users SET password = ? WHERE user_id = ?");
                $stmt->execute([$new_password, $user_id]); // In a real app, use password_hash()

                $success = "Password updated successfully";
            } catch (PDOException $e) {
                $error = "Error updating password: " . $e->getMessage();
            }
        } else {
            $error = "Current password is incorrect";
        }
    }
}

// Get success or error messages
$success = isset($success) ? $success : '';
$error = isset($error) ? $error : '';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - Profile</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/user.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Additional styles for profile page */
        .profile-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
        }

        .profile-sidebar {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            height: fit-content;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            background-color: var(--accent-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--primary-color);
            font-size: 48px;
        }

        .profile-info {
            text-align: center;
            margin-bottom: 20px;
        }

        .profile-info h3 {
            color: var(--primary-color);
            margin: 0 0 5px 0;
        }

        .profile-info p {
            color: var(--secondary-color);
            margin: 0;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 20px;
        }

        .stat-item {
            background-color: var(--background-color);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-item h4 {
            font-size: 24px;
            margin: 0 0 5px 0;
            color: var(--primary-color);
        }

        .stat-item p {
            margin: 0;
            color: var(--secondary-color);
            font-size: 14px;
        }

        .profile-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .password-form {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }

        .password-form h3 {
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .profile-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Your Profile</p>
            </div>
            <div class="user-profile">
                <span class="user-name"><i class="fas fa-user-graduate"></i> <?php echo htmlspecialchars($username); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <?php
                $active_page = 'profile';
                include('includes/sidebar.php');
                ?>

                <div class="content">
                    <div class="page-header">
                        <h2>Your Profile</h2>
                    </div>

                    <?php if (!empty($success)): ?>
                        <div class="success-message">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error)): ?>
                        <div class="error-message">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <div class="profile-container">
                        <div class="profile-sidebar">
                            <div class="profile-avatar">
                                <i class="fas fa-user-graduate"></i>
                            </div>
                            <div class="profile-info">
                                <h3><?php echo htmlspecialchars($username); ?></h3>
                                <p>Student</p>
                            </div>
                            <div class="profile-stats">
                                <div class="stat-item">
                                    <h4><?php echo $module_count; ?></h4>
                                    <p>Modules</p>
                                </div>
                                <div class="stat-item">
                                    <h4><?php echo $note_count; ?></h4>
                                    <p>Notes</p>
                                </div>
                            </div>
                        </div>

                        <div class="profile-content">
                            <div class="password-form">
                                <h3>Change Password</h3>
                                <form action="profile.php" method="post">
                                    <input type="hidden" name="action" value="change_password">
                                    <div class="form-group">
                                        <label for="current_password">Current Password</label>
                                        <input type="password" id="current_password" name="current_password" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="new_password">New Password</label>
                                        <input type="password" id="new_password" name="new_password" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="confirm_password">Confirm New Password</label>
                                        <input type="password" id="confirm_password" name="confirm_password" required>
                                    </div>
                                    <div class="form-buttons">
                                        <button type="submit" class="btn btn-slim">Update Password</button>
                                    </div>
                                </form>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h3>Account Information</h3>
                                </div>
                                <div class="card-body">
                                    <p><strong>Username:</strong> <?php echo htmlspecialchars($username); ?></p>
                                    <p><strong>Account Created:</strong>
                                        <?php
                                        $stmt = $conn->prepare("SELECT created_at FROM users WHERE user_id = ?");
                                        $stmt->execute([$user_id]);
                                        $created_at = $stmt->fetch()['created_at'];
                                        echo date('F d, Y', strtotime($created_at));
                                        ?>
                                    </p>
                                    <p><strong>Last Login:</strong> <?php echo date('F d, Y'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <script src="js/user.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Password validation
            const newPasswordInput = document.getElementById('new_password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const passwordForm = document.querySelector('.password-form form');

            if (passwordForm) {
                passwordForm.addEventListener('submit', function(e) {
                    if (newPasswordInput.value !== confirmPasswordInput.value) {
                        e.preventDefault();
                        alert('New passwords do not match!');
                    }
                });
            }

            // Password strength indicator
            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;

                    // Add strength for length
                    if (password.length >= 8) strength += 1;

                    // Add strength for containing numbers
                    if (/\d/.test(password)) strength += 1;

                    // Add strength for containing special characters
                    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1;

                    // Add strength for containing uppercase and lowercase
                    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength += 1;

                    // Update UI based on strength
                    let strengthText = '';
                    let strengthColor = '';

                    switch (strength) {
                        case 0:
                        case 1:
                            strengthText = 'Weak';
                            strengthColor = '#dc3545';
                            break;
                        case 2:
                            strengthText = 'Moderate';
                            strengthColor = '#ffc107';
                            break;
                        case 3:
                            strengthText = 'Strong';
                            strengthColor = '#28a745';
                            break;
                        case 4:
                            strengthText = 'Very Strong';
                            strengthColor = '#198754';
                            break;
                    }

                    // Create or update strength indicator
                    let indicator = document.getElementById('password-strength');
                    if (!indicator) {
                        indicator = document.createElement('div');
                        indicator.id = 'password-strength';
                        indicator.style.marginTop = '5px';
                        indicator.style.fontSize = '14px';
                        this.parentNode.appendChild(indicator);
                    }

                    indicator.textContent = `Password Strength: ${strengthText}`;
                    indicator.style.color = strengthColor;
                });
            }
        });
    </script>
</body>
</html>
