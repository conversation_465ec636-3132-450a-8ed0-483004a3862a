<?php
/**
 * AI Functions
 * 
 * This file contains functions for AI features in the StudyNotes application.
 */

require_once('openai_handler.php');

/**
 * Generate a quiz based on note content
 * 
 * @param PDO $conn Database connection
 * @param int $note_id The ID of the note to generate quiz from
 * @param int $user_id The ID of the user
 * @param string $quiz_title The title for the quiz
 * @param int $num_questions Number of questions to generate
 * @param string $difficulty Difficulty level (easy, medium, hard)
 * @param string $question_type Type of questions (multiple_choice, short_answer)
 * @return array|bool Array with quiz_id and questions on success, false on failure
 */
function generateQuizFromNote($conn, $note_id, $user_id, $quiz_title, $num_questions = 5, $difficulty = 'medium', $question_type = 'multiple_choice') {
    try {
        // Get note content
        $stmt = $conn->prepare("
            SELECT n.*, m.module_id 
            FROM notes n
            JOIN modules m ON n.module_id = m.module_id
            WHERE n.note_id = ? AND n.user_id = ?
        ");
        $stmt->execute([$note_id, $user_id]);
        $note = $stmt->fetch();
        
        if (!$note) {
            return false;
        }
        
        // Initialize OpenAI handler
        $ai = new OpenAIHandler();
        
        // Generate questions
        $questions = $ai->generateQuizQuestions(
            $note['content'], 
            $num_questions, 
            $difficulty, 
            $question_type
        );
        
        if (!$questions) {
            return false;
        }
        
        // Begin transaction
        $conn->beginTransaction();
        
        // Create quiz
        $stmt = $conn->prepare("
            INSERT INTO quizzes (user_id, module_id, quiz_title) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$user_id, $note['module_id'], $quiz_title]);
        $quiz_id = $conn->lastInsertId();
        
        // Add questions
        foreach ($questions as $question) {
            $options = null;
            if ($question_type == 'multiple_choice' && isset($question['options'])) {
                $options = json_encode($question['options']);
            }
            
            $stmt = $conn->prepare("
                INSERT INTO quiz_questions (
                    quiz_id, 
                    question_text, 
                    question_type, 
                    difficulty, 
                    correct_answer, 
                    options
                ) VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $quiz_id, 
                $question['question_text'], 
                $question_type, 
                $difficulty, 
                $question['correct_answer'], 
                $options
            ]);
        }
        
        // Commit transaction
        $conn->commit();
        
        return [
            'quiz_id' => $quiz_id,
            'questions' => $questions
        ];
    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log("Error generating quiz: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate a summary for a note
 * 
 * @param PDO $conn Database connection
 * @param int $note_id The ID of the note to summarize
 * @param int $user_id The ID of the user
 * @return string|bool The summary on success, false on failure
 */
function generateNoteSummary($conn, $note_id, $user_id) {
    try {
        // Get note content
        $stmt = $conn->prepare("SELECT * FROM notes WHERE note_id = ? AND user_id = ?");
        $stmt->execute([$note_id, $user_id]);
        $note = $stmt->fetch();
        
        if (!$note) {
            return false;
        }
        
        // Initialize OpenAI handler
        $ai = new OpenAIHandler();
        
        // Generate summary
        $summary = $ai->generateSummary($note['content']);
        
        if (!$summary) {
            return false;
        }
        
        // Check if summary already exists
        $stmt = $conn->prepare("SELECT * FROM ai_summaries WHERE note_id = ?");
        $stmt->execute([$note_id]);
        $existing_summary = $stmt->fetch();
        
        if ($existing_summary) {
            // Update existing summary
            $stmt = $conn->prepare("
                UPDATE ai_summaries 
                SET summary_content = ? 
                WHERE summary_id = ?
            ");
            $stmt->execute([$summary, $existing_summary['summary_id']]);
        } else {
            // Create new summary
            $stmt = $conn->prepare("
                INSERT INTO ai_summaries (note_id, summary_content) 
                VALUES (?, ?)
            ");
            $stmt->execute([$note_id, $summary]);
        }
        
        return $summary;
    } catch (PDOException $e) {
        error_log("Error generating summary: " . $e->getMessage());
        return false;
    }
}

/**
 * Get chat response from AI
 * 
 * @param PDO $conn Database connection
 * @param int $user_id The ID of the user
 * @param string $message The user's message
 * @param array $chat_history Previous messages in the conversation
 * @return string|bool The AI response on success, false on failure
 */
function getChatResponse($conn, $user_id, $message, $chat_history = []) {
    try {
        // Initialize OpenAI handler
        $ai = new OpenAIHandler();
        
        // Get relevant context from user's notes
        $context = getRelevantContext($conn, $user_id, $message);
        
        // Generate response
        $response = $ai->generateChatResponse($message, $context, $chat_history);
        
        if (!$response) {
            return false;
        }
        
        // Save to chat history
        $stmt = $conn->prepare("
            INSERT INTO ai_chat_history (user_id, message_text, response_text) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$user_id, $message, $response]);
        
        return $response;
    } catch (PDOException $e) {
        error_log("Error getting chat response: " . $e->getMessage());
        return false;
    }
}

/**
 * Get relevant context from user's notes based on query
 * 
 * @param PDO $conn Database connection
 * @param int $user_id The ID of the user
 * @param string $query The user's query
 * @return array Array of relevant content
 */
function getRelevantContext($conn, $user_id, $query) {
    // Simple keyword-based retrieval for now
    // In a production environment, consider using embeddings or a more sophisticated retrieval method
    
    $keywords = extractKeywords($query);
    $context = [];
    
    if (empty($keywords)) {
        // If no keywords, return recent notes
        $stmt = $conn->prepare("
            SELECT n.note_id, n.title, n.content
            FROM notes n
            WHERE n.user_id = ?
            ORDER BY n.updated_at DESC
            LIMIT 3
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetchAll();
    }
    
    // Build query with keywords
    $sql = "
        SELECT n.note_id, n.title, n.content
        FROM notes n
        WHERE n.user_id = ? AND (
    ";
    
    $conditions = [];
    $params = [$user_id];
    
    foreach ($keywords as $keyword) {
        $conditions[] = "n.title LIKE ? OR n.content LIKE ?";
        $params[] = "%{$keyword}%";
        $params[] = "%{$keyword}%";
    }
    
    $sql .= implode(" OR ", $conditions);
    $sql .= ") ORDER BY n.updated_at DESC LIMIT 5";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Extract keywords from a query
 * 
 * @param string $query The query to extract keywords from
 * @return array Array of keywords
 */
function extractKeywords($query) {
    // Remove common words and keep only significant terms
    $stopwords = ['a', 'an', 'the', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 
                 'be', 'been', 'being', 'in', 'on', 'at', 'to', 'for', 'with', 
                 'about', 'against', 'between', 'into', 'through', 'during', 
                 'before', 'after', 'above', 'below', 'from', 'up', 'down', 
                 'of', 'off', 'over', 'under', 'again', 'further', 'then', 
                 'once', 'here', 'there', 'when', 'where', 'why', 'how', 
                 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 
                 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 
                 'so', 'than', 'too', 'very', 'can', 'will', 'just', 'should', 
                 'now', 'what', 'which', 'who', 'whom'];
    
    // Convert to lowercase and remove punctuation
    $query = strtolower($query);
    $query = preg_replace('/[^\w\s]/', '', $query);
    
    // Split into words
    $words = preg_split('/\s+/', $query);
    
    // Filter out stopwords and short words
    $keywords = array_filter($words, function($word) use ($stopwords) {
        return !in_array($word, $stopwords) && strlen($word) > 2;
    });
    
    return array_values($keywords);
}
