<?php
session_start();
require_once('../db_connection.php');
require_once('../includes/functions.php');
require_once('../includes/ai_functions.php');

// Check if user is logged in as admin
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header('Location: login.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Update AI service settings
    if (isset($_POST['ai_service'])) {
        $ai_config['service'] = $_POST['ai_service'];

        // Update the API key if provided
        if (!empty($_POST['api_key'])) {
            $ai_config['api_key'] = $_POST['api_key'];
        }

        // Update temperature if provided
        if (isset($_POST['temperature'])) {
            $ai_config['temperature'] = floatval($_POST['temperature']);
        }

        // Update max tokens if provided
        if (isset($_POST['max_tokens'])) {
            $ai_config['max_tokens'] = intval($_POST['max_tokens']);
        }

        // Update use_fallback setting
        $ai_config['use_fallback'] = isset($_POST['use_fallback']) && $_POST['use_fallback'] === 'on';

        // Save the updated configuration to a file
        $config_file = '../includes/ai_config.php';
        $config_content = "<?php\n// AI Configuration - Auto-generated file\n\$ai_config = " . var_export($ai_config, true) . ";\n?>";

        if (file_put_contents($config_file, $config_content)) {
            $success_message = "AI settings updated successfully!";
        } else {
            $error_message = "Failed to save AI settings. Please check file permissions.";
        }
    }
}

// Include header
$page_title = "AI Settings";
$active_page = "ai_settings";
include('../includes/admin_header.php');
?>

<div class="content-wrapper">
    <div class="content-header">
        <h1>AI Settings</h1>
        <p>Configure the AI service used for generating quizzes and summaries.</p>
    </div>

    <?php if (isset($success_message)): ?>
    <div class="alert alert-success">
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <div class="content-body">
        <div class="card">
            <div class="card-header">
                <h2>AI Service Configuration</h2>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="form-group">
                        <label for="ai_service">AI Service:</label>
                        <select name="ai_service" id="ai_service" class="form-control">
                            <option value="claude" <?php echo $ai_config['service'] === 'claude' ? 'selected' : ''; ?>>Claude</option>
                            <option value="deepseek" <?php echo $ai_config['service'] === 'deepseek' ? 'selected' : ''; ?>>DeepSeek</option>
                            <option value="grok" <?php echo $ai_config['service'] === 'grok' ? 'selected' : ''; ?>>Grok 3</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="api_key">API Key:</label>
                        <input type="text" name="api_key" id="api_key" class="form-control" placeholder="Enter new API key (leave empty to keep current)" />
                        <small class="form-text text-muted">Current key: <?php echo substr($ai_config['api_key'], 0, 5) . '...' . substr($ai_config['api_key'], -5); ?></small>
                    </div>

                    <div class="form-group">
                        <label for="temperature">Temperature:</label>
                        <input type="number" name="temperature" id="temperature" class="form-control" value="<?php echo $ai_config['temperature']; ?>" min="0" max="1" step="0.1" />
                        <small class="form-text text-muted">Controls randomness: 0 = deterministic, 1 = maximum creativity</small>
                    </div>

                    <div class="form-group">
                        <label for="max_tokens">Max Tokens:</label>
                        <input type="number" name="max_tokens" id="max_tokens" class="form-control" value="<?php echo $ai_config['max_tokens']; ?>" min="100" max="8000" step="100" />
                        <small class="form-text text-muted">Maximum length of the AI response</small>
                    </div>

                    <div class="form-group">
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" name="use_fallback" <?php echo $ai_config['use_fallback'] ? 'checked' : ''; ?> />
                                Use fallback mode (generate simple responses without calling the API)
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Save Settings</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h2>Test AI Service</h2>
            </div>
            <div class="card-body">
                <p>Use this form to test the current AI service configuration.</p>

                <form id="testAiForm">
                    <div class="form-group">
                        <label for="test_prompt">Test Prompt:</label>
                        <textarea name="test_prompt" id="test_prompt" class="form-control" rows="3" placeholder="Enter a prompt to test the AI service"></textarea>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-secondary">Test AI</button>
                    </div>
                </form>

                <div id="testResults" class="mt-3" style="display: none;">
                    <h3>Results:</h3>
                    <div class="alert alert-info">
                        <div id="testResultsContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const testForm = document.getElementById('testAiForm');
    const testResults = document.getElementById('testResults');
    const testResultsContent = document.getElementById('testResultsContent');

    if (testForm) {
        testForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const prompt = document.getElementById('test_prompt').value;

            if (!prompt) {
                alert('Please enter a prompt to test.');
                return;
            }

            // Show loading indicator
            testResults.style.display = 'block';
            testResultsContent.innerHTML = '<p>Testing AI service... Please wait.</p>';

            // Send test request
            fetch('../api/test_ai.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ prompt: prompt })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    testResultsContent.innerHTML = '<p><strong>AI Response:</strong></p><p>' + data.response + '</p>';
                } else {
                    testResultsContent.innerHTML = '<p class="text-danger">Error: ' + (data.error || 'Failed to get response from AI service') + '</p>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                testResultsContent.innerHTML = '<p class="text-danger">Error: Failed to communicate with the server.</p>';
            });
        });
    }
});
</script>

<?php include('../includes/admin_footer.php'); ?>
