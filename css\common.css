/* 
 * Common Styles for StudyNotes Application
 * Consolidated styles to reduce duplication across CSS files
 */

/* CSS Variables - Single source of truth */
:root {
    --primary-color: #65350F;       /* Dark Brown */
    --secondary-color: #A67B5B;     /* Medium Brown */
    --accent-color: #D4A373;        /* Light Brown */
    --highlight-color: #E8871E;     /* Orange */
    --text-color: #2D2424;          /* Dark Brown Text */
    --light-text: #F5EBE0;          /* Light Text */
    --background-color: #F5EBE0;    /* Light Beige */
    --card-color: #FFFFFF;          /* White */
    --danger-color: #dc3545;        /* Red for delete actions */
    --success-color: #28a745;       /* Green for success messages */
    --warning-color: #ffc107;       /* Yellow for warnings */
}

/* Common Card Styles */
.card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

/* Standardized Content Cards (Notes, Quizzes, Summaries) */
.content-card {
    background-color: var(--card-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(212, 163, 115, 0.2);
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--highlight-color));
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.content-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.content-card-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
    flex: 1;
    line-height: 1.3;
}

.content-card-body {
    color: var(--text-color);
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
    flex-grow: 1;
    overflow: hidden;
}

.content-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    margin-top: auto;
}

.content-card-date {
    color: var(--secondary-color);
    display: flex;
    align-items: center;
}

.content-card-date i {
    margin-right: 5px;
    color: var(--highlight-color);
}

.content-card-actions {
    display: flex;
    gap: 5px;
}

/* Standardized Action Buttons */
.btn-sm {
    background-color: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.2s ease;
}

.btn-sm:hover {
    background-color: var(--primary-color);
    color: #ffffff !important;
    transform: translateY(-1px);
}

.btn-sm i {
    margin-right: 4px;
    font-size: 12px;
}

.btn-sm.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-sm.btn-danger:hover {
    background-color: #c82333;
    color: white;
}

/* Standardized Button System */
.btn-primary {
    background-color: var(--primary-color);
    color: var(--light-text);
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    min-width: 120px;
}

.btn-primary:hover {
    background-color: var(--highlight-color);
    color: var(--light-text);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background-color: #f8f9fa;
    color: var(--text-color);
    border: 1px solid #dee2e6;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    min-width: 120px;
}

.btn-secondary:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.btn-nav {
    background-color: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.2s ease;
}

.btn-nav:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
    transform: translateY(-1px);
}

.btn-nav i {
    margin-right: 6px;
    font-size: 12px;
}

/* Button sizing modifiers */
.btn-full-width {
    width: 100%;
}

.btn-large {
    padding: 16px 24px;
    font-size: 18px;
}

/* Standardized Profile Components */
.profile-grid {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
    margin: 20px 0;
}

.profile-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--accent-color);
    height: fit-content;
}

.profile-avatar {
    text-align: center;
    margin-bottom: 20px;
}

.profile-avatar i {
    font-size: 64px;
    color: var(--primary-color);
    background-color: rgba(101, 53, 15, 0.1);
    border-radius: 50%;
    width: 100px;
    height: 100px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.profile-info {
    text-align: center;
    margin-bottom: 25px;
}

.profile-info h3 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
    font-size: 22px;
}

.profile-info p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 14px;
}

.profile-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.profile-stat-item {
    text-align: center;
    padding: 15px;
    background-color: rgba(101, 53, 15, 0.05);
    border-radius: 6px;
}

.profile-stat-item h4 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
    font-size: 24px;
    font-weight: bold;
}

.profile-stat-item p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Content View Cards (for individual pages) */
.content-view-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--accent-color);
    margin-bottom: 25px;
}

.content-view-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.content-view-title {
    color: var(--primary-color);
    margin: 0 0 10px 0;
    font-size: 28px;
    line-height: 1.3;
}

.content-view-meta {
    display: flex;
    align-items: center;
    gap: 20px;
    color: var(--secondary-color);
    font-size: 14px;
}

.content-view-meta i {
    color: var(--highlight-color);
    margin-right: 5px;
}

.content-view-body {
    line-height: 1.8;
    color: var(--text-color);
    font-size: 16px;
}

.content-view-actions {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Module-specific styling within content cards */
.module-stats {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.note-count {
    color: var(--secondary-color);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
}

.note-count i {
    margin-right: 5px;
    color: var(--highlight-color);
}

/* Legacy card styles for backward compatibility */
.card-header {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.card-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
    flex: 1;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 15px;
}

/* Grid Layouts */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Module Badge */
.module-badge {
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    margin-left: 10px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--accent-color);
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

/* Loading Spinner */
.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--highlight-color);
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Generation Progress */
.generation-progress {
    text-align: center;
    padding: 30px 20px;
}

.generation-progress p {
    margin-bottom: 5px;
    color: var(--primary-color);
    font-weight: 500;
}

.generation-progress p.small {
    font-size: 14px;
    color: var(--secondary-color);
}

/* Message Styles */
.message {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border-left: 4px solid;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    border-left-color: var(--success-color);
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border-left-color: var(--danger-color);
}

.warning-message {
    background-color: #fff3cd;
    color: #856404;
    border-left-color: var(--warning-color);
}

/* Filter Badge */
.filter-badge {
    display: inline-flex;
    align-items: center;
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 10px;
}

.filter-badge a {
    color: var(--primary-color);
    margin-left: 5px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
}

.filter-badge a:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

/* Page Actions */
.page-actions {
    display: flex;
    gap: 10px;
}

/* Relationship Card */
.relationship-card {
    display: flex;
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
}

.relationship-icon {
    font-size: 24px;
    color: var(--primary-color);
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: rgba(101, 53, 15, 0.1);
    border-radius: 50%;
}

.relationship-content {
    flex: 1;
}

.relationship-content h4 {
    margin-top: 0;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.relationship-content p {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--secondary-color);
}

/* Question/Content Expansion */
.expandable-content {
    max-height: 200px;
    overflow-y: auto;
    transition: max-height 0.3s ease;
}

.expandable-content.expanded {
    max-height: 800px;
}

.expand-collapse-btn {
    text-align: center;
    margin-top: 10px;
}

/* Option Items */
.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    transition: background-color 0.2s, border-color 0.2s;
}

.option-item:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
}

.option-item input[type="radio"] {
    margin-right: 10px;
}

.option-item label {
    flex: 1;
    cursor: pointer;
    font-size: 16px;
    margin: 0;
}

/* Result Options */
.option-result {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.option-marker {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.option-text {
    flex: 1;
}

.correct-option {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 3px solid var(--success-color);
}

.incorrect-option {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 3px solid var(--danger-color);
}

/* Module-specific styling (preserving original design) */
.module-card {
    background-color: var(--card-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(212, 163, 115, 0.2);
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.module-header {
    margin-bottom: 10px;
}

.module-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
}

.module-description {
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    flex-grow: 1;
}

.module-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 13px;
    color: var(--secondary-color);
}

.module-meta i {
    margin-right: 5px;
    color: var(--highlight-color);
}

.module-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-actions {
    display: flex;
    gap: 5px;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    color: var(--primary-color);
    background-color: rgba(0, 0, 0, 0.05);
}

.btn-icon.delete-module-btn:hover {
    color: var(--danger-color);
}

/* Legacy note card styling for dashboard compatibility */
.note-card {
    background-color: var(--card-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(212, 163, 115, 0.2);
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.note-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--highlight-color));
}

.note-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.note-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
    flex: 1;
}

.note-preview {
    color: var(--text-color);
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
    flex-grow: 1;
    overflow: hidden;
}

.note-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.note-date {
    color: var(--secondary-color);
    display: flex;
    align-items: center;
}

.note-date i {
    margin-right: 5px;
    color: var(--highlight-color);
}

.note-actions {
    display: flex;
    gap: 5px;
}

/* Grid layouts for legacy compatibility */
.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* Standardized Feature Cards (Landing Page) */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin: 8px auto;
    max-width: 750px;
}

.feature-card {
    background-color: var(--card-color);
    border-radius: 10px;
    padding: 22px 17px;
    width: 264px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-top: 5px solid var(--accent-color);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.feature-card i {
    font-size: 42px;
    color: var(--highlight-color);
    margin-bottom: 10px;
    display: block;
}

.feature-card h3 {
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 18px;
    font-weight: 600;
}

.feature-card p {
    color: var(--secondary-color);
    line-height: 1.6;
    font-size: 15px;
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    margin: 15px 0;
    padding: 0 20px;
}

.welcome-section h2 {
    color: var(--primary-color);
    font-size: 38px;
    margin-bottom: 8px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    display: inline-block;
    padding-bottom: 12px;
}

.welcome-section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--highlight-color);
}

.welcome-section p {
    color: var(--secondary-color);
    font-size: 16px;
    line-height: 1.5;
    max-width: 650px;
    margin: 0 auto 8px auto;
    font-weight: 400;
}

/* Features Section (Reference Design) */
.features {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
    margin: 20px auto 8px auto;
    max-width: 850px;
}

/* Access Section (Reference Design) */
.access-section {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin: 12px auto;
    max-width: 650px;
}

.access-card {
    background-color: var(--card-color);
    border-radius: 10px;
    padding: 24px 20px;
    width: 286px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    border-bottom: 5px solid var(--highlight-color);
}

.access-card.admin {
    border-bottom-color: var(--primary-color);
}

.access-card:hover {
    transform: translateY(-5px);
}

.access-card h3 {
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: 600;
}

.access-card p {
    color: var(--secondary-color);
    margin-bottom: 15px;
    line-height: 1.4;
    font-size: 14px;
}

/* Reference Design Buttons */
.btn {
    display: inline-block;
    padding: 0.8rem 2rem;
    background-color: var(--highlight-color);
    color: var(--light-text);
    text-decoration: none;
    border-radius: 50px;
    font-weight: bold;
    transition: all 0.3s;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background-color: var(--primary-color);
    color: #ffffff;
    transform: translateY(-3px);
    text-decoration: none;
}

.admin-btn {
    background-color: var(--primary-color);
}

.admin-btn:hover {
    background-color: var(--secondary-color);
}

/* Standardized Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background-color: var(--card-color);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(212, 163, 115, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--highlight-color));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    background-color: rgba(101, 53, 15, 0.1);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.stat-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.stat-info h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 24px;
    font-weight: bold;
}

.stat-info p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 14px;
}

/* Standardized Form Cards */
.form-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
    padding: 20px;
}

.form-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 30px;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-top: 4px solid var(--accent-color);
}

.form-card h2 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 25px;
    font-size: 24px;
}

.form-card h2 i {
    margin-right: 10px;
    color: var(--highlight-color);
}

/* Standardized Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--accent-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(212, 163, 115, 0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
}

/* Responsive Design for All Components */
@media (max-width: 768px) {
    .grid-container, .notes-grid, .modules-grid, .feature-grid, .access-grid, .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .welcome-section {
        margin: 12px 0;
    }

    .welcome-section h2 {
        font-size: 32px;
        margin-bottom: 6px;
    }

    .welcome-section p {
        margin-bottom: 6px;
        font-size: 15px;
    }

    .features {
        margin: 15px auto 6px auto;
        gap: 20px;
    }

    .access-section {
        margin: 8px auto;
        gap: 25px;
    }

    .content-card, .note-card, .module-card, .feature-card, .access-card, .stat-card {
        padding: 15px;
    }

    .content-card-header h4, .note-header h4, .module-header h4, .feature-card h3, .access-card h3 {
        font-size: 16px;
    }

    .content-card-body, .note-preview, .module-description, .feature-card p, .access-card p {
        font-size: 13px;
    }

    .content-card-actions, .note-actions, .module-actions {
        flex-wrap: wrap;
        gap: 5px;
    }

    .btn-sm, .btn-icon, .btn-nav {
        padding: 6px 10px;
        font-size: 12px;
    }

    .btn-primary, .btn-secondary {
        padding: 10px 16px;
        font-size: 14px;
        min-width: 100px;
    }

    .module-badge {
        font-size: 11px;
        padding: 2px 6px;
    }

    .profile-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .profile-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .form-card {
        padding: 20px;
        margin: 10px;
    }

    .content-view-card {
        padding: 20px;
    }

    .content-view-title {
        font-size: 24px;
    }

    .content-view-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .welcome-section {
        margin: 8px 0;
    }

    .welcome-section h2 {
        font-size: 28px;
        margin-bottom: 5px;
    }

    .welcome-section p {
        margin-bottom: 5px;
        font-size: 14px;
        line-height: 1.4;
    }

    .features {
        margin: 12px auto 5px auto;
        gap: 15px;
    }

    .access-section {
        margin: 6px auto;
        gap: 20px;
    }

    .feature-card {
        padding: 17px 13px;
        width: 100%;
        max-width: 308px;
    }

    .feature-card i {
        font-size: 36px;
        margin-bottom: 8px;
    }

    .feature-card h3 {
        font-size: 16px;
        margin-bottom: 6px;
    }

    .access-card {
        padding: 20px 17px;
        width: 100%;
        max-width: 315px;
    }

    .access-card h3 {
        font-size: 18px;
        margin-bottom: 6px;
    }

    .access-card p {
        font-size: 13px;
        margin-bottom: 12px;
    }

    .content-card-header, .note-header, .module-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .content-card-footer, .note-footer, .module-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .content-card-actions, .note-actions, .module-actions {
        width: 100%;
        justify-content: flex-start;
    }
}
