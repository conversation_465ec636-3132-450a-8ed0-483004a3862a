<?php
session_start();
require_once('db_connection.php');
require_once('includes/functions.php');
require_once('includes/ai_functions.php');

// Check if user is logged in
if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Check if site is in maintenance mode
check_maintenance_mode($conn);

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Check if AI features are enabled
try {
    $stmt = $conn->prepare("SELECT setting_value FROM ai_settings WHERE setting_name = 'enable_ai_features'");
    $stmt->execute();
    $ai_enabled = $stmt->fetchColumn();
    
    if (!$ai_enabled) {
        header('Location: dashboard.php?error=AI features are currently disabled');
        exit;
    }
} catch (PDOException $e) {
    // Default to enabled if setting doesn't exist
    $ai_enabled = true;
}

// Get default AI settings
try {
    $stmt = $conn->prepare("SELECT setting_name, setting_value FROM ai_settings");
    $stmt->execute();
    $ai_settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (PDOException $e) {
    $ai_settings = [
        'default_quiz_questions' => 5,
        'default_quiz_difficulty' => 'medium',
        'default_quiz_type' => 'multiple_choice'
    ];
}

// Get all modules for this user (for dropdown)
$stmt = $conn->prepare("SELECT * FROM modules WHERE user_id = ? ORDER BY module_name ASC");
$stmt->execute([$user_id]);
$modules = $stmt->fetchAll();

// Get notes for selected module
$module_id = isset($_GET['module']) ? intval($_GET['module']) : null;
$notes = [];

if ($module_id) {
    $stmt = $conn->prepare("
        SELECT n.*, m.module_name
        FROM notes n
        JOIN modules m ON n.module_id = m.module_id
        WHERE n.user_id = ? AND n.module_id = ?
        ORDER BY n.title ASC
    ");
    $stmt->execute([$user_id, $module_id]);
    $notes = $stmt->fetchAll();
}

// Get existing quizzes
$stmt = $conn->prepare("
    SELECT q.*, m.module_name, 
           (SELECT COUNT(*) FROM quiz_questions WHERE quiz_id = q.quiz_id) as question_count
    FROM quizzes q
    JOIN modules m ON q.module_id = m.module_id
    WHERE q.user_id = ?
    ORDER BY q.created_at DESC
");
$stmt->execute([$user_id]);
$quizzes = $stmt->fetchAll();

// Handle quiz generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'generate_quiz') {
    $note_id = isset($_POST['note_id']) ? intval($_POST['note_id']) : null;
    $quiz_title = isset($_POST['quiz_title']) ? trim($_POST['quiz_title']) : '';
    $num_questions = isset($_POST['num_questions']) ? intval($_POST['num_questions']) : 5;
    $difficulty = isset($_POST['difficulty']) ? $_POST['difficulty'] : 'medium';
    $question_type = isset($_POST['question_type']) ? $_POST['question_type'] : 'multiple_choice';
    
    // Validate input
    if (empty($note_id) || empty($quiz_title)) {
        $error = "Note and quiz title are required";
    } else {
        // Generate quiz
        $result = generateQuizFromNote(
            $conn, 
            $note_id, 
            $user_id, 
            $quiz_title, 
            $num_questions, 
            $difficulty, 
            $question_type
        );
        
        if ($result) {
            header('Location: view_quiz.php?id=' . $result['quiz_id'] . '&success=Quiz generated successfully');
            exit;
        } else {
            $error = "Failed to generate quiz. Please try again.";
        }
    }
}

// Handle quiz deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_quiz') {
    $quiz_id = isset($_POST['quiz_id']) ? intval($_POST['quiz_id']) : null;
    
    if ($quiz_id) {
        try {
            // Begin transaction
            $conn->beginTransaction();
            
            // Delete quiz questions
            $stmt = $conn->prepare("DELETE FROM quiz_questions WHERE quiz_id = ?");
            $stmt->execute([$quiz_id]);
            
            // Delete quiz
            $stmt = $conn->prepare("DELETE FROM quizzes WHERE quiz_id = ? AND user_id = ?");
            $stmt->execute([$quiz_id, $user_id]);
            
            // Commit transaction
            $conn->commit();
            
            header('Location: ai_quiz.php?success=Quiz deleted successfully');
            exit;
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            $error = "Error deleting quiz: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - AI Quiz Generator</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/user.css">
    <link rel="stylesheet" href="css/form-elements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .quiz-card {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            padding: 15px;
            margin-bottom: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid var(--highlight-color);
        }
        
        .quiz-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .quiz-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .quiz-title {
            font-size: 18px;
            color: var(--primary-color);
            margin: 0;
        }
        
        .quiz-meta {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: var(--secondary-color);
        }
        
        .quiz-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .ai-icon {
            color: var(--highlight-color);
            margin-right: 5px;
        }
        
        .note-selector {
            display: none;
        }
        
        .note-selector.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Student Dashboard</p>
            </div>
            <div class="user-profile">
                <span class="user-name"><i class="fas fa-user-graduate"></i> <?php echo htmlspecialchars($username); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        <a href="modules.php"><i class="fas fa-book"></i> Modules</a>
                        <a href="notes.php"><i class="fas fa-sticky-note"></i> Notes</a>
                        <a href="ai_quiz.php" class="active"><i class="fas fa-robot"></i> AI Quiz</a>
                        <a href="ai_summary.php"><i class="fas fa-robot"></i> AI Summary</a>
                        <a href="ai_chat.php"><i class="fas fa-robot"></i> AI Chat</a>
                        <a href="profile.php"><i class="fas fa-user"></i> Profile</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2><i class="fas fa-robot ai-icon"></i> AI Quiz Generator</h2>
                        <button id="generateQuizBtn" class="btn"><i class="fas fa-plus"></i> Generate New Quiz</button>
                    </div>

                    <?php if (!empty($error)): ?>
                        <div class="error-message">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_GET['success'])): ?>
                        <div class="success-message">
                            <?php echo htmlspecialchars($_GET['success']); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Quiz List -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Your Quizzes</h3>
                        </div>
                        <div class="card-body">
                            <?php if (count($quizzes) > 0): ?>
                                <?php foreach ($quizzes as $quiz): ?>
                                    <div class="quiz-card">
                                        <div class="quiz-header">
                                            <h4 class="quiz-title"><?php echo htmlspecialchars($quiz['quiz_title']); ?></h4>
                                        </div>
                                        <div class="quiz-meta">
                                            <span><i class="fas fa-book"></i> <?php echo htmlspecialchars($quiz['module_name']); ?></span>
                                            <span><i class="fas fa-question-circle"></i> <?php echo $quiz['question_count']; ?> questions</span>
                                            <span><i class="fas fa-calendar-alt"></i> <?php echo date('M d, Y', strtotime($quiz['created_at'])); ?></span>
                                        </div>
                                        <div class="quiz-actions">
                                            <a href="view_quiz.php?id=<?php echo $quiz['quiz_id']; ?>" class="btn-sm"><i class="fas fa-eye"></i> View</a>
                                            <a href="take_quiz.php?id=<?php echo $quiz['quiz_id']; ?>" class="btn-sm"><i class="fas fa-play"></i> Take Quiz</a>
                                            <button class="btn-sm btn-danger delete-quiz-btn" data-id="<?php echo $quiz['quiz_id']; ?>" data-title="<?php echo htmlspecialchars($quiz['quiz_title']); ?>">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="no-data">You haven't generated any quizzes yet. Click "Generate New Quiz" to create your first quiz.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <!-- Generate Quiz Modal -->
    <div id="generateQuizModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-robot ai-icon"></i> Generate AI Quiz</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form action="ai_quiz.php" method="post">
                    <input type="hidden" name="action" value="generate_quiz">
                    
                    <div class="form-group">
                        <label for="module_id">Select Module</label>
                        <select id="module_id" name="module_id" required>
                            <option value="">-- Select Module --</option>
                            <?php foreach ($modules as $module): ?>
                                <option value="<?php echo $module['module_id']; ?>" <?php echo ($module_id == $module['module_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($module['module_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div id="noteSelector" class="form-group note-selector">
                        <label for="note_id">Select Note</label>
                        <select id="note_id" name="note_id" required>
                            <option value="">-- Select Note --</option>
                            <?php foreach ($notes as $note): ?>
                                <option value="<?php echo $note['note_id']; ?>">
                                    <?php echo htmlspecialchars($note['title']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="quiz_title">Quiz Title</label>
                        <input type="text" id="quiz_title" name="quiz_title" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="num_questions">Number of Questions</label>
                        <input type="number" id="num_questions" name="num_questions" min="1" max="20" value="<?php echo $ai_settings['default_quiz_questions'] ?? 5; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="difficulty">Difficulty Level</label>
                        <select id="difficulty" name="difficulty" required>
                            <option value="easy" <?php echo ($ai_settings['default_quiz_difficulty'] == 'easy') ? 'selected' : ''; ?>>Easy</option>
                            <option value="medium" <?php echo ($ai_settings['default_quiz_difficulty'] == 'medium') ? 'selected' : ''; ?>>Medium</option>
                            <option value="hard" <?php echo ($ai_settings['default_quiz_difficulty'] == 'hard') ? 'selected' : ''; ?>>Hard</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="question_type">Question Type</label>
                        <select id="question_type" name="question_type" required>
                            <option value="multiple_choice" <?php echo ($ai_settings['default_quiz_type'] == 'multiple_choice') ? 'selected' : ''; ?>>Multiple Choice</option>
                            <option value="short_answer" <?php echo ($ai_settings['default_quiz_type'] == 'short_answer') ? 'selected' : ''; ?>>Short Answer</option>
                        </select>
                    </div>
                    
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn"><i class="fas fa-robot"></i> Generate Quiz</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Quiz Modal -->
    <div id="deleteQuizModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-trash"></i> Delete Quiz</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the quiz "<span id="deleteQuizTitle"></span>"?</p>
                <p>This action cannot be undone.</p>
                
                <form action="ai_quiz.php" method="post">
                    <input type="hidden" name="action" value="delete_quiz">
                    <input type="hidden" id="deleteQuizId" name="quiz_id">
                    
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Quiz</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/user.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Generate Quiz Button
            const generateQuizBtn = document.getElementById('generateQuizBtn');
            const generateQuizModal = document.getElementById('generateQuizModal');
            
            if (generateQuizBtn && generateQuizModal) {
                generateQuizBtn.addEventListener('click', function() {
                    openModalWithAnimation(generateQuizModal);
                });
            }
            
            // Module selection changes note options
            const moduleSelect = document.getElementById('module_id');
            const noteSelector = document.getElementById('noteSelector');
            
            if (moduleSelect) {
                moduleSelect.addEventListener('change', function() {
                    const moduleId = this.value;
                    if (moduleId) {
                        window.location.href = 'ai_quiz.php?module=' + moduleId;
                    }
                });
            }
            
            // Show note selector if module is selected
            if (moduleSelect && moduleSelect.value && noteSelector) {
                noteSelector.classList.add('active');
            }
            
            // Delete Quiz Button
            const deleteQuizBtns = document.querySelectorAll('.delete-quiz-btn');
            const deleteQuizModal = document.getElementById('deleteQuizModal');
            const deleteQuizTitle = document.getElementById('deleteQuizTitle');
            const deleteQuizId = document.getElementById('deleteQuizId');
            
            deleteQuizBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const quizId = this.getAttribute('data-id');
                    const quizTitle = this.getAttribute('data-title');
                    
                    if (deleteQuizTitle) deleteQuizTitle.textContent = quizTitle;
                    if (deleteQuizId) deleteQuizId.value = quizId;
                    
                    if (deleteQuizModal) {
                        openModalWithAnimation(deleteQuizModal);
                    }
                });
            });
        });
    </script>
</body>
</html>
