<?php
session_start();
require_once('db_connection.php');
require_once('includes/functions.php');
require_once('includes/ai_functions.php');

// Check if user is logged in
if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Check if site is in maintenance mode
check_maintenance_mode($conn);

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Check if AI features are enabled
try {
    $stmt = $conn->prepare("SELECT setting_value FROM ai_settings WHERE setting_name = 'enable_ai_features'");
    $stmt->execute();
    $ai_enabled = $stmt->fetchColumn();
    
    if (!$ai_enabled) {
        header('Location: dashboard.php?error=AI features are currently disabled');
        exit;
    }
} catch (PDOException $e) {
    // Default to enabled if setting doesn't exist
    $ai_enabled = true;
}

// Get chat history
$stmt = $conn->prepare("
    SELECT * FROM ai_chat_history
    WHERE user_id = ?
    ORDER BY created_at DESC
    LIMIT 10
");
$stmt->execute([$user_id]);
$chat_history = $stmt->fetchAll();

// Handle new message
$response = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'send_message') {
    $message = isset($_POST['message']) ? trim($_POST['message']) : '';
    
    if (!empty($message)) {
        // Format chat history for AI
        $formatted_history = [];
        foreach (array_reverse($chat_history) as $chat) {
            $formatted_history[] = [
                'role' => 'user',
                'content' => $chat['message_text']
            ];
            $formatted_history[] = [
                'role' => 'assistant',
                'content' => $chat['response_text']
            ];
        }
        
        // Get response from AI
        $response = getChatResponse($conn, $user_id, $message, $formatted_history);
        
        if ($response) {
            // Refresh chat history
            $stmt = $conn->prepare("
                SELECT * FROM ai_chat_history
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT 10
            ");
            $stmt->execute([$user_id]);
            $chat_history = $stmt->fetchAll();
        } else {
            $error = "Failed to get a response. Please try again.";
        }
    } else {
        $error = "Please enter a message";
    }
}

// Clear chat history
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'clear_history') {
    try {
        $stmt = $conn->prepare("DELETE FROM ai_chat_history WHERE user_id = ?");
        $stmt->execute([$user_id]);
        
        // Refresh page
        header('Location: ai_chat.php?success=Chat history cleared');
        exit;
    } catch (PDOException $e) {
        $error = "Error clearing chat history: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - AI Chat Assistant</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/user.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .chat-container {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            height: calc(100vh - 250px);
            min-height: 500px;
        }
        
        .chat-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-header h3 {
            margin: 0;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }
        
        .chat-header .ai-icon {
            margin-right: 10px;
            color: var(--highlight-color);
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column-reverse;
        }
        
        .message {
            max-width: 80%;
            margin-bottom: 15px;
            padding: 12px 15px;
            border-radius: 10px;
            position: relative;
            line-height: 1.5;
        }
        
        .user-message {
            background-color: var(--highlight-color);
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 0;
        }
        
        .ai-message {
            background-color: #f0f0f0;
            color: var(--text-color);
            align-self: flex-start;
            border-bottom-left-radius: 0;
        }
        
        .message-time {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.5);
            margin-top: 5px;
            text-align: right;
        }
        
        .user-message .message-time {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .chat-input {
            padding: 15px;
            border-top: 1px solid #eee;
            display: flex;
        }
        
        .chat-input input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 16px;
            outline: none;
        }
        
        .chat-input input:focus {
            border-color: var(--highlight-color);
            box-shadow: 0 0 0 2px rgba(232, 135, 30, 0.1);
        }
        
        .chat-input button {
            background-color: var(--highlight-color);
            color: white;
            border: none;
            border-radius: 30px;
            padding: 12px 20px;
            margin-left: 10px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .chat-input button:hover {
            background-color: var(--primary-color);
        }
        
        .chat-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 15px;
        }
        
        .welcome-message {
            text-align: center;
            padding: 30px;
            color: var(--secondary-color);
        }
        
        .welcome-message h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .welcome-message .ai-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }
        
        .suggestions {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .suggestion-chip {
            background-color: #f0f0f0;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .suggestion-chip:hover {
            background-color: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Student Dashboard</p>
            </div>
            <div class="user-profile">
                <span class="user-name"><i class="fas fa-user-graduate"></i> <?php echo htmlspecialchars($username); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        <a href="modules.php"><i class="fas fa-book"></i> Modules</a>
                        <a href="notes.php"><i class="fas fa-sticky-note"></i> Notes</a>
                        <a href="ai_quiz.php"><i class="fas fa-robot"></i> AI Quiz</a>
                        <a href="ai_summary.php"><i class="fas fa-robot"></i> AI Summary</a>
                        <a href="ai_chat.php" class="active"><i class="fas fa-robot"></i> AI Chat</a>
                        <a href="profile.php"><i class="fas fa-user"></i> Profile</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2><i class="fas fa-robot ai-icon"></i> AI Chat Assistant</h2>
                        <button id="clearHistoryBtn" class="btn"><i class="fas fa-trash"></i> Clear History</button>
                    </div>

                    <?php if (!empty($error)): ?>
                        <div class="error-message">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_GET['success'])): ?>
                        <div class="success-message">
                            <?php echo htmlspecialchars($_GET['success']); ?>
                        </div>
                    <?php endif; ?>

                    <div class="chat-container">
                        <div class="chat-header">
                            <h3><i class="fas fa-robot ai-icon"></i> Study Assistant</h3>
                        </div>
                        
                        <div class="chat-messages" id="chatMessages">
                            <?php if (count($chat_history) > 0): ?>
                                <?php foreach ($chat_history as $chat): ?>
                                    <div class="message ai-message">
                                        <?php echo nl2br(htmlspecialchars($chat['response_text'])); ?>
                                        <div class="message-time"><?php echo date('h:i A', strtotime($chat['created_at'])); ?></div>
                                    </div>
                                    <div class="message user-message">
                                        <?php echo nl2br(htmlspecialchars($chat['message_text'])); ?>
                                        <div class="message-time"><?php echo date('h:i A', strtotime($chat['created_at'])); ?></div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="welcome-message">
                                    <i class="fas fa-robot ai-icon"></i>
                                    <h3>Welcome to the AI Study Assistant!</h3>
                                    <p>Ask me anything about your study materials, and I'll help you understand concepts, explain difficult topics, or find information in your notes.</p>
                                    
                                    <div class="suggestions">
                                        <div class="suggestion-chip" onclick="suggestQuestion('Can you explain the key concepts in my latest note?')">Explain key concepts</div>
                                        <div class="suggestion-chip" onclick="suggestQuestion('How do I prepare for an exam on this material?')">Exam preparation</div>
                                        <div class="suggestion-chip" onclick="suggestQuestion('What are the most important points to remember?')">Important points</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <form action="ai_chat.php" method="post" class="chat-input">
                            <input type="hidden" name="action" value="send_message">
                            <input type="text" name="message" id="messageInput" placeholder="Type your message here..." autocomplete="off" required>
                            <button type="submit"><i class="fas fa-paper-plane"></i></button>
                        </form>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <!-- Clear History Modal -->
    <div id="clearHistoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-trash"></i> Clear Chat History</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to clear your entire chat history?</p>
                <p>This action cannot be undone.</p>
                
                <form action="ai_chat.php" method="post">
                    <input type="hidden" name="action" value="clear_history">
                    
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn btn-danger">Clear History</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/user.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Clear History Button
            const clearHistoryBtn = document.getElementById('clearHistoryBtn');
            const clearHistoryModal = document.getElementById('clearHistoryModal');
            
            if (clearHistoryBtn && clearHistoryModal) {
                clearHistoryBtn.addEventListener('click', function() {
                    openModalWithAnimation(clearHistoryModal);
                });
            }
            
            // Focus on message input
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.focus();
            }
        });
        
        // Suggestion chip functionality
        function suggestQuestion(question) {
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.value = question;
                messageInput.focus();
            }
        }
    </script>
</body>
</html>
