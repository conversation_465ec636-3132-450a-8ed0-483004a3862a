/**
 * Enhanced User Dashboard JavaScript
 * With improved animations, interactivity, and visual effects
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all dashboard components
    initDashboard();

    // Setup enhanced modal functionality
    setupModals();

    // Configure data tables with sorting and filtering
    setupDataTables();

    // Add animation effects to dashboard elements
    animateDashboardElements();

    // Setup notification system
    setupNotifications();

    // Add motion effects to actions
    setupActionEffects();

    // Setup auto-save for forms
    setupFormAutoSave();

    // Add keyboard shortcuts for power users
    setupKeyboardShortcuts();

    // Add dashboard easter egg
    console.log('%c Welcome to StudyNotes! ', 'background: linear-gradient(to right, #65350F, #A67B5B); color: white; font-size: 1.2rem; padding: 5px; border-radius: 5px;');
});

/**
 * Initialize dashboard components
 */
function initDashboard() {
    // Initialize sidebar menu with active state
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.sidebar-menu a');

    sidebarLinks.forEach(link => {
        const href = link.getAttribute('href');

        // Set active class based on current path
        if (href === currentPath || currentPath.includes(href)) {
            link.classList.add('active');

            // Add animated highlight effect to active menu item
            link.style.position = 'relative';

            const activeHighlight = document.createElement('span');
            activeHighlight.style.cssText = `
                position: absolute;
                left: 0;
                top: 0;
                width: 0;
                height: 100%;
                background-color: rgba(101, 53, 15, 0.1);
                z-index: -1;
                border-radius: 0 4px 4px 0;
                transition: width 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            `;

            link.appendChild(activeHighlight);

            setTimeout(() => {
                activeHighlight.style.width = '100%';
            }, 300);
        }

        // Add hover animation
        link.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.paddingLeft = '2rem';
                this.style.backgroundColor = 'rgba(101, 53, 15, 0.03)';
            }
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.paddingLeft = '1.5rem';
                this.style.backgroundColor = 'transparent';
            }
        });
    });

    // Add collapsible sidebar functionality for mobile
    const sidebarToggle = document.createElement('button');
    sidebarToggle.classList.add('sidebar-toggle');
    sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
    sidebarToggle.style.cssText = `
        display: none;
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        z-index: 100;
        transition: transform 0.3s;
    `;

    document.body.appendChild(sidebarToggle);

    const sidebar = document.querySelector('.sidebar');

    // Show toggle button on mobile
    if (window.innerWidth <= 992) {
        sidebarToggle.style.display = 'flex';
        sidebarToggle.style.alignItems = 'center';
        sidebarToggle.style.justifyContent = 'center';

        // Initially hide sidebar on mobile
        if (sidebar) {
            sidebar.style.transform = 'translateX(-110%)';
            sidebar.style.position = 'fixed';
            sidebar.style.top = '0';
            sidebar.style.left = '0';
            sidebar.style.height = '100vh';
            sidebar.style.zIndex = '99';
            sidebar.style.transition = 'transform 0.3s cubic-bezier(0.65, 0, 0.35, 1)';
        }
    }

    sidebarToggle.addEventListener('click', function() {
        if (sidebar) {
            if (sidebar.style.transform === 'translateX(-110%)') {
                sidebar.style.transform = 'translateX(0)';
                this.innerHTML = '<i class="fas fa-times"></i>';
                this.style.transform = 'rotate(90deg)';
            } else {
                sidebar.style.transform = 'translateX(-110%)';
                this.innerHTML = '<i class="fas fa-bars"></i>';
                this.style.transform = 'rotate(0)';
            }
        }
    });

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 992 && sidebar &&
            !sidebar.contains(e.target) &&
            e.target !== sidebarToggle &&
            !sidebarToggle.contains(e.target)) {
            sidebar.style.transform = 'translateX(-110%)';
            sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
            sidebarToggle.style.transform = 'rotate(0)';
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 992) {
            sidebarToggle.style.display = 'flex';

            if (sidebar) {
                sidebar.style.position = 'fixed';
                sidebar.style.transition = 'transform 0.3s cubic-bezier(0.65, 0, 0.35, 1)';
            }
        } else {
            sidebarToggle.style.display = 'none';

            if (sidebar) {
                sidebar.style.transform = 'translateX(0)';
                sidebar.style.position = 'static';
                sidebar.style.height = 'auto';
            }
        }
    });

    // Add fade-in animation to dashboard elements
    document.querySelectorAll('.card, .stat-card').forEach((element, index) => {
        element.style.opacity = '0';
        element.style.animation = `fadeIn 0.5s ease forwards ${index * 0.1}s`;
    });

    // Add event listeners for interactive elements
    document.querySelectorAll('.note-card, .module-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';
        });
    });
}

/**
 * Setup enhanced modal functionality
 */
function setupModals() {
    // Get all modals
    const addModuleModal = document.getElementById('addModuleModal');
    const editModuleModal = document.getElementById('editModuleModal');
    const deleteModuleModal = document.getElementById('deleteModuleModal');

    // Get all modal triggers
    const addModuleBtn = document.getElementById('addModuleBtn');
    const editModuleBtns = document.querySelectorAll('.edit-module-btn');
    const deleteModuleBtns = document.querySelectorAll('.delete-module-btn');

    // Get all close buttons
    const closeBtns = document.querySelectorAll('.close');
    const cancelBtns = document.querySelectorAll('.cancel-btn');

    // Store all modals in array for easier access
    const modals = [addModuleModal, editModuleModal, deleteModuleModal].filter(modal => modal);

    // Add Module Button Click with enhanced animation
    if (addModuleBtn) {
        addModuleBtn.addEventListener('click', function() {
            if (addModuleModal) {
                // Show modal with advanced animation
                openModalWithAnimation(addModuleModal);
            }
        });
    }

    // Edit Module Button Click with data loading animation
    editModuleBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const moduleId = this.getAttribute('data-id');
            const moduleName = this.getAttribute('data-name');
            const moduleDescription = this.getAttribute('data-description');

            if (editModuleModal) {
                // Show loading animation
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                btn.disabled = true;

                // Simulate data loading (in real app, this would be an API call)
                setTimeout(() => {
                    document.getElementById('edit_module_id').value = moduleId;
                    document.getElementById('edit_module_name').value = moduleName;
                    document.getElementById('edit_module_description').value = moduleDescription || '';

                    // Reset button and open modal
                    btn.innerHTML = '<i class="fas fa-edit"></i>';
                    btn.disabled = false;

                    openModalWithAnimation(editModuleModal);
                }, 300);
            }
        });
    });

    // Delete Module Button Click
    deleteModuleBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const moduleId = this.getAttribute('data-id');
            const moduleName = this.getAttribute('data-name');

            if (deleteModuleModal) {
                document.getElementById('delete_module_id').value = moduleId;
                document.getElementById('delete_module_name').textContent = moduleName;

                openModalWithAnimation(deleteModuleModal);
            }
        });
    });

    // Close button click
    closeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModalWithAnimation(modal);
        });
    });

    // Cancel button click
    cancelBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModalWithAnimation(modal);
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        modals.forEach(modal => {
            if (event.target === modal) {
                closeModalWithAnimation(modal);
            }
        });
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            modals.forEach(modal => {
                if (modal.style.display === 'block') {
                    closeModalWithAnimation(modal);
                }
            });
        }
    });
}

/**
 * Open modal with animation
 */
function openModalWithAnimation(modal) {
    if (!modal) return;

    // Reset any existing animation classes
    modal.classList.remove('modal-closing');

    // Set the modal display to block first
    modal.style.display = 'block';

    // Get the modal content for animation
    const modalContent = modal.querySelector('.modal-content');

    if (modalContent) {
        // Reset any existing animations
        modalContent.style.animation = 'none';

        // Trigger reflow to restart animation
        void modalContent.offsetWidth;

        // Apply enhanced animation
        modalContent.style.animation = 'modalFadeIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards';

        // Add blur effect to background
        document.querySelector('main').style.filter = 'blur(2px)';
        document.querySelector('main').style.transition = 'filter 0.3s';
    }

    // Focus first input
    setTimeout(() => {
        const firstInput = modal.querySelector('input, textarea');
        if (firstInput) {
            firstInput.focus();
        }
    }, 300);
}

/**
 * Close modal with animation
 */
function closeModalWithAnimation(modal) {
    if (!modal) return;

    // Add closing class to modal
    modal.classList.add('modal-closing');

    // Get the modal content
    const modalContent = modal.querySelector('.modal-content');

    if (modalContent) {
        // Apply closing animation
        modalContent.style.animation = 'modalFadeOut 0.3s forwards';

        // Remove blur effect from background
        document.querySelector('main').style.filter = 'blur(0)';

        // Define the fade out animation if not already in CSS
        if (!document.querySelector('style#modal-animations')) {
            const style = document.createElement('style');
            style.id = 'modal-animations';
            style.innerHTML = `
                @keyframes modalFadeOut {
                    from {opacity: 1; transform: translateY(0);}
                    to {opacity: 0; transform: translateY(-20px);}
                }
            `;
            document.head.appendChild(style);
        }

        // Wait for animation to complete before hiding modal
        setTimeout(() => {
            modal.style.display = 'none';
            modal.classList.remove('warning-modal', 'modal-closing');
        }, 300);
    } else {
        // Fallback if modalContent not found
        modal.style.display = 'none';
    }
}

/**
 * Setup data tables with sorting and filtering
 */
function setupDataTables() {
    // Add sorting functionality to tables
    document.querySelectorAll('th[data-sortable="true"]').forEach(th => {
        th.style.cursor = 'pointer';
        th.addEventListener('click', function() {
            const table = this.closest('table');
            const index = Array.from(this.parentNode.children).indexOf(this);
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            const isAsc = this.classList.contains('asc');

            // Remove sort classes from all headers
            table.querySelectorAll('th').forEach(header => {
                header.classList.remove('asc', 'desc');
            });

            // Add sort class to current header
            this.classList.add(isAsc ? 'desc' : 'asc');

            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.children[index].textContent.trim();
                const bValue = b.children[index].textContent.trim();

                // Check if values are numbers
                if (!isNaN(aValue) && !isNaN(bValue)) {
                    return isAsc ? bValue - aValue : aValue - bValue;
                }

                // Sort as strings
                return isAsc ?
                    bValue.localeCompare(aValue) :
                    aValue.localeCompare(bValue);
            });

            // Reorder rows
            const tbody = table.querySelector('tbody');
            rows.forEach(row => tbody.appendChild(row));

            // Add animation to sorted rows
            rows.forEach((row, i) => {
                row.style.animation = `fadeIn 0.3s ease forwards ${i * 0.05}s`;
            });
        });
    });
}

/**
 * Add animation effects to dashboard elements
 */
function animateDashboardElements() {
    // Add CSS for animations if not already present
    if (!document.getElementById('dashboard-animations')) {
        const style = document.createElement('style');
        style.id = 'dashboard-animations';
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }

            @keyframes slideDown {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }

            @keyframes slideUp {
                from { transform: translateY(0); opacity: 1; }
                to { transform: translateY(-50px); opacity: 0; }
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            @keyframes modalFadeIn {
                from {opacity: 0; transform: translateY(-20px);}
                to {opacity: 1; transform: translateY(0);}
            }

            .stat-card:hover .stat-icon {
                animation: pulse 1s infinite;
            }
        `;
        document.head.appendChild(style);
    }

    // Add hover effects to buttons
    document.querySelectorAll('.btn, .btn-sm').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // Add animation to the "Add New Module" button
    const addModuleBtn = document.getElementById('addModuleBtn');
    if (addModuleBtn) {
        addModuleBtn.style.position = 'relative';
        addModuleBtn.style.overflow = 'hidden';

        // Add hover effect
        addModuleBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';

            // Add subtle background animation
            const highlight = document.createElement('span');
            highlight.style.cssText = `
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 255, 255, 0.2),
                    transparent
                );
                transition: left 0.5s;
            `;
            this.appendChild(highlight);

            // Animate the highlight
            setTimeout(() => {
                highlight.style.left = '100%';
            }, 50);

            // Remove the highlight after animation
            setTimeout(() => {
                highlight.remove();
            }, 550);
        });

        addModuleBtn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    }
}

/**
 * Setup notification system
 */
function setupNotifications() {
    // Auto-hide success and error messages after 5 seconds
    const messages = document.querySelectorAll('.success-message, .error-message');

    messages.forEach(message => {
        setTimeout(() => {
            message.style.animation = 'fadeOut 0.5s ease forwards';
            setTimeout(() => {
                message.style.display = 'none';
            }, 500);
        }, 5000);
    });
}

/**
 * Add motion effects to actions
 */
function setupActionEffects() {
    // Add ripple effect to buttons
    document.querySelectorAll('.btn, .btn-sm, .btn-icon').forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);

            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${e.clientX - rect.left - size / 2}px`;
            ripple.style.top = `${e.clientY - rect.top - size / 2}px`;

            ripple.classList.add('active');

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add CSS for ripple effect
    if (!document.getElementById('ripple-effect')) {
        const style = document.createElement('style');
        style.id = 'ripple-effect';
        style.textContent = `
            .btn, .btn-sm, .btn-icon {
                position: relative;
                overflow: hidden;
            }

            .ripple {
                position: absolute;
                border-radius: 50%;
                background-color: rgba(255, 255, 255, 0.4);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Setup auto-save for forms
 */
function setupFormAutoSave() {
    // Auto-save form data to localStorage
    document.querySelectorAll('form').forEach(form => {
        // Skip forms that shouldn't be auto-saved (like delete forms)
        if (form.closest('#deleteModuleModal')) return;

        const formId = form.id || `form_${Math.random().toString(36).substr(2, 9)}`;
        form.id = formId;

        // Load saved data
        const savedData = localStorage.getItem(`autosave_${formId}`);
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                Object.keys(data).forEach(key => {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input && !['hidden', 'submit', 'button'].includes(input.type)) {
                        input.value = data[key];
                    }
                });
            } catch (e) {
                console.error('Error loading autosaved data', e);
            }
        }

        // Save data on input change
        form.querySelectorAll('input, textarea, select').forEach(input => {
            if (['hidden', 'submit', 'button'].includes(input.type)) return;

            input.addEventListener('input', function() {
                const formData = {};
                form.querySelectorAll('input, textarea, select').forEach(el => {
                    if (['hidden', 'submit', 'button'].includes(el.type)) return;
                    if (el.name) {
                        formData[el.name] = el.value;
                    }
                });

                localStorage.setItem(`autosave_${formId}`, JSON.stringify(formData));
            });
        });

        // Clear saved data on submit
        form.addEventListener('submit', function() {
            localStorage.removeItem(`autosave_${formId}`);
        });
    });
}

/**
 * Setup keyboard shortcuts for power users
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Alt + N: New Module
        if (e.altKey && e.key === 'n') {
            e.preventDefault();
            const addModuleBtn = document.getElementById('addModuleBtn');
            if (addModuleBtn) {
                addModuleBtn.click();
            }
        }

        // Alt + H: Go to Dashboard
        if (e.altKey && e.key === 'h') {
            e.preventDefault();
            window.location.href = 'dashboard.php';
        }

        // Alt + M: Go to Modules
        if (e.altKey && e.key === 'm') {
            e.preventDefault();
            window.location.href = 'modules.php';
        }

        // Alt + T: Go to Notes
        if (e.altKey && e.key === 't') {
            e.preventDefault();
            window.location.href = 'notes.php';
        }
    });

    // Add tooltip to show keyboard shortcuts
    const addModuleBtn = document.getElementById('addModuleBtn');
    if (addModuleBtn) {
        addModuleBtn.setAttribute('title', 'Add New Module (Alt+N)');
    }
}
