<?php
session_start();
require_once('db_connection.php');
require_once('includes/functions.php');

// If user is an admin, redirect to dashboard
if (is_admin()) {
    header('Location: admin/dashboard.php');
    exit;
}

// Get site settings
try {
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_name = 'site_name'");
    $stmt->execute();
    $site_name = $stmt->fetchColumn();
    
    if (!$site_name) {
        $site_name = 'StudyNotes';
    }
} catch (PDOException $e) {
    $site_name = 'StudyNotes';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($site_name); ?> - Maintenance Mode</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .maintenance-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 60vh;
            padding: 2rem;
            text-align: center;
        }
        
        .maintenance-icon {
            font-size: 5rem;
            color: var(--highlight-color);
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        .maintenance-title {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .maintenance-message {
            font-size: 1.2rem;
            color: var(--secondary-color);
            max-width: 600px;
            margin: 0 auto 2rem;
            line-height: 1.6;
        }
        
        .admin-login-link {
            margin-top: 2rem;
        }
        
        .admin-login-link a {
            color: var(--highlight-color);
            text-decoration: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .admin-login-link a:hover {
            color: var(--primary-color);
        }
        
        .admin-login-link a i {
            margin-right: 0.5rem;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1><?php echo htmlspecialchars($site_name); ?></h1>
                <p class="tagline">Temporarily Unavailable</p>
            </div>
        </header>

        <main>
            <div class="maintenance-container">
                <div class="maintenance-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h2 class="maintenance-title">Site Under Maintenance</h2>
                <p class="maintenance-message">
                    We're currently performing scheduled maintenance to improve your experience.
                    Please check back soon. We apologize for any inconvenience.
                </p>
                <div class="admin-login-link">
                    <a href="admin/login.php"><i class="fas fa-user-shield"></i> Administrator Login</a>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 <?php echo htmlspecialchars($site_name); ?>. All rights reserved.</p>
        </footer>
    </div>
</body>
</html>
