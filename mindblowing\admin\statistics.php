<?php
session_start();
require_once('../db_connection.php');

// Check if user is logged in as admin
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header('Location: login.php');
    exit;
}

// Get total users count
$stmt = $conn->prepare("SELECT COUNT(*) as user_count FROM users");
$stmt->execute();
$user_count = $stmt->fetch()['user_count'];

// Get total modules count
$stmt = $conn->prepare("SELECT COUNT(*) as module_count FROM modules");
$stmt->execute();
$module_count = $stmt->fetch()['module_count'];

// Get total notes count
$stmt = $conn->prepare("SELECT COUNT(*) as note_count FROM notes");
$stmt->execute();
$note_count = $stmt->fetch()['note_count'];

// Get recent users
$stmt = $conn->prepare("SELECT * FROM users ORDER BY created_at DESC LIMIT 5");
$stmt->execute();
$recent_users = $stmt->fetchAll();

// Get most active users (users with most notes)
$stmt = $conn->prepare("
    SELECT u.user_id, u.username, COUNT(n.note_id) as note_count
    FROM users u
    LEFT JOIN notes n ON u.user_id = n.user_id
    GROUP BY u.user_id
    ORDER BY note_count DESC
    LIMIT 5
");
$stmt->execute();
$active_users = $stmt->fetchAll();

// Get most popular modules (modules with most notes)
$stmt = $conn->prepare("
    SELECT m.module_id, m.module_name, COUNT(n.note_id) as note_count
    FROM modules m
    LEFT JOIN notes n ON m.module_id = n.module_id
    GROUP BY m.module_id
    ORDER BY note_count DESC
    LIMIT 5
");
$stmt->execute();
$popular_modules = $stmt->fetchAll();

// Get monthly statistics (notes created per month)
$stmt = $conn->prepare("
    SELECT
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as count
    FROM notes
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month ASC
");
$stmt->execute();
$monthly_stats = $stmt->fetchAll();

// Format data for chart
$chart_labels = [];
$chart_data = [];
foreach ($monthly_stats as $stat) {
    $chart_labels[] = date('M Y', strtotime($stat['month'] . '-01'));
    $chart_data[] = $stat['count'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - Admin Statistics</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #65350F;
            --secondary-color: #A67B5B;
            --highlight-color: #E8871E;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background-color: var(--card-color);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            display: flex;
            align-items: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: rgba(101, 53, 15, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .stat-info h3 {
            font-size: 1.8rem;
            margin: 0;
            color: var(--primary-color);
        }

        .stat-info p {
            margin: 0.3rem 0 0;
            color: var(--secondary-color);
        }

        .chart-container {
            background-color: var(--card-color);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .chart-header h3 {
            margin: 0;
            color: var(--primary-color);
        }

        .chart-body {
            position: relative;
            height: 300px;
        }

        .top-list {
            background-color: var(--card-color);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .top-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .top-list-header h3 {
            margin: 0;
            color: var(--primary-color);
        }

        .top-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 0;
            border-bottom: 1px solid #eee;
        }

        .top-list-item:last-child {
            border-bottom: none;
        }

        .top-list-item-info {
            display: flex;
            align-items: center;
        }

        .top-list-item-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(101, 53, 15, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1rem;
            color: var(--primary-color);
        }

        .top-list-item-name {
            font-weight: 500;
        }

        .top-list-item-count {
            background-color: rgba(101, 53, 15, 0.1);
            color: var(--primary-color);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .two-column-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
        }

        @media (max-width: 768px) {
            .two-column-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Admin Dashboard</p>
            </div>
            <div class="admin-profile">
                <span class="admin-name"><i class="fas fa-user-shield"></i> <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-users"></i> User Management</a>
                        <a href="statistics.php" class="active"><i class="fas fa-chart-line"></i> Statistics</a>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                        <a href="ai_settings.php"><i class="fas fa-robot"></i> AI Settings</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2>System Statistics</h2>
                        <div class="date-range">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Last 6 Months</span>
                        </div>
                    </div>

                    <!-- Stats Overview -->
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3><?php echo $user_count; ?></h3>
                                <p>Total Users</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="stat-info">
                                <h3><?php echo $module_count; ?></h3>
                                <p>Total Modules</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-sticky-note"></i>
                            </div>
                            <div class="stat-info">
                                <h3><?php echo $note_count; ?></h3>
                                <p>Total Notes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Activity Chart -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Monthly Activity</h3>
                            <div class="chart-actions">
                                <button class="chart-toggle-btn" id="toggleChartType">
                                    <i class="fas fa-chart-bar"></i> <span>Switch to Line Chart</span>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <canvas id="activityChart"></canvas>
                        </div>
                    </div>

                    <!-- Two Column Layout for Lists -->
                    <div class="two-column-grid">
                        <!-- Most Active Users -->
                        <div class="top-list">
                            <div class="top-list-header">
                                <h3>Most Active Users</h3>
                            </div>
                            <?php if (count($active_users) > 0): ?>
                                <?php foreach ($active_users as $user): ?>
                                    <div class="top-list-item">
                                        <div class="top-list-item-info">
                                            <div class="top-list-item-icon">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="top-list-item-name">
                                                <?php echo htmlspecialchars($user['username']); ?>
                                            </div>
                                        </div>
                                        <div class="top-list-item-count">
                                            <?php echo $user['note_count']; ?> notes
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="no-data">No active users found.</div>
                            <?php endif; ?>
                        </div>

                        <!-- Popular Modules -->
                        <div class="top-list">
                            <div class="top-list-header">
                                <h3>Popular Modules</h3>
                            </div>
                            <?php if (count($popular_modules) > 0): ?>
                                <?php foreach ($popular_modules as $module): ?>
                                    <div class="top-list-item">
                                        <div class="top-list-item-info">
                                            <div class="top-list-item-icon">
                                                <i class="fas fa-book"></i>
                                            </div>
                                            <div class="top-list-item-name">
                                                <?php echo htmlspecialchars($module['module_name']); ?>
                                            </div>
                                        </div>
                                        <div class="top-list-item-count">
                                            <?php echo $module['note_count']; ?> notes
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="no-data">No popular modules found.</div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Recent Users -->
                    <div class="top-list">
                        <div class="top-list-header">
                            <h3>Recently Joined Users</h3>
                        </div>
                        <?php if (count($recent_users) > 0): ?>
                            <div class="top-list-grid">
                                <?php foreach ($recent_users as $user): ?>
                                    <div class="top-list-item">
                                        <div class="top-list-item-info">
                                            <div class="top-list-item-icon">
                                                <i class="fas fa-user-plus"></i>
                                            </div>
                                            <div class="top-list-item-name">
                                                <?php echo htmlspecialchars($user['username']); ?>
                                            </div>
                                        </div>
                                        <div class="top-list-item-date">
                                            <?php echo date('M d, Y', strtotime($user['created_at'])); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="no-data">No recent users found.</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <script src="js/admin-animations.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the activity chart
            const ctx = document.getElementById('activityChart').getContext('2d');
            let chartType = 'bar';

            const chartData = {
                labels: <?php echo json_encode($chart_labels); ?>,
                datasets: [{
                    label: 'Notes Created',
                    data: <?php echo json_encode($chart_data); ?>,
                    backgroundColor: 'rgba(232, 135, 30, 0.2)',
                    borderColor: 'rgba(232, 135, 30, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(232, 135, 30, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7
                }]
            };

            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeOutQuart'
                }
            };

            const activityChart = new Chart(ctx, {
                type: chartType,
                data: chartData,
                options: chartOptions
            });

            // Toggle chart type
            document.getElementById('toggleChartType').addEventListener('click', function() {
                chartType = chartType === 'bar' ? 'line' : 'bar';
                activityChart.destroy();

                const newChart = new Chart(ctx, {
                    type: chartType,
                    data: chartData,
                    options: chartOptions
                });

                activityChart = newChart;

                // Update button icon and text
                const icon = this.querySelector('i');
                const text = this.querySelector('span');

                if (chartType === 'bar') {
                    icon.className = 'fas fa-chart-bar';
                    text.textContent = 'Switch to Line Chart';
                } else {
                    icon.className = 'fas fa-chart-line';
                    text.textContent = 'Switch to Bar Chart';
                }

                // Add animation effect
                this.classList.add('btn-animated');
                setTimeout(() => {
                    this.classList.remove('btn-animated');
                }, 500);
            });

            // Add animation to stat cards
            document.querySelectorAll('.stat-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100 * index);
            });
        });
    </script>
</body>
</html>
