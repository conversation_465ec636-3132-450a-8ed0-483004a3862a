<?php
/**
 * Utility functions for StudyNotes application
 */

/**
 * Check if the site is in maintenance mode
 * 
 * @param PDO $conn Database connection
 * @return bool True if site is in maintenance mode, false otherwise
 */
function is_maintenance_mode($conn) {
    try {
        // Check if settings table exists
        $stmt = $conn->prepare("
            SELECT COUNT(*) FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'settings'
        ");
        $stmt->execute();
        $table_exists = $stmt->fetchColumn() > 0;
        
        if ($table_exists) {
            // Get maintenance mode setting
            $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_name = 'maintenance_mode'");
            $stmt->execute();
            $maintenance_mode = $stmt->fetchColumn();
            
            return $maintenance_mode == 1;
        }
    } catch (PDOException $e) {
        // If there's an error, assume site is not in maintenance mode
        error_log("Error checking maintenance mode: " . $e->getMessage());
    }
    
    return false;
}

/**
 * Check if current user is an admin
 * 
 * @return bool True if current user is an admin, false otherwise
 */
function is_admin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

/**
 * Redirect to maintenance page if site is in maintenance mode and user is not an admin
 * 
 * @param PDO $conn Database connection
 */
function check_maintenance_mode($conn) {
    if (is_maintenance_mode($conn) && !is_admin()) {
        // Redirect to maintenance page
        header('Location: maintenance.php');
        exit;
    }
}
