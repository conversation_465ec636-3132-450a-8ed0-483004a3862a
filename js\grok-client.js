/**
 * Grok 3 API Client for StudyNotes
 * 
 * This file contains functions for interacting with the GitHub Grok 3 API
 * for generating quizzes and summaries.
 */

/**
 * Call the Grok 3 API
 * 
 * @param {string} prompt - The prompt to send to Grok 3
 * @param {string} token - The GitHub token for authentication
 * @param {Object} options - Additional options for the API call
 * @returns {Promise<string>} - The response from Grok 3
 */
async function callGrokApi(prompt, token, options = {}) {
    const endpoint = "https://models.github.ai/inference";
    const model = "xai/grok-3";
    
    // Default options
    const defaultOptions = {
        temperature: 1,
        top_p: 1
    };
    
    // Merge default options with provided options
    const apiOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(`${endpoint}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                messages: [
                    { role: "system", content: options.systemPrompt || "" },
                    { role: "user", content: prompt }
                ],
                temperature: apiOptions.temperature,
                top_p: apiOptions.top_p,
                model: model
            })
        });
        
        if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}`);
        }
        
        const data = await response.json();
        return data.choices[0].message.content;
    } catch (error) {
        console.error("Error calling Grok 3 API:", error);
        throw error;
    }
}

/**
 * Generate a quiz using Grok 3
 * 
 * @param {string} noteContent - The content of the note
 * @param {string} noteTitle - The title of the note
 * @param {number} numQuestions - Number of questions to generate
 * @param {string} difficulty - Difficulty level (easy, medium, hard)
 * @param {string} token - The GitHub token for authentication
 * @returns {Promise<Array>} - Array of questions and answers
 */
async function generateQuizWithGrok(noteContent, noteTitle, numQuestions = 5, difficulty = 'medium', token) {
    // Create the prompt for Grok 3
    const prompt = `Based on the following study note titled '${noteTitle}', generate ${numQuestions} ${difficulty} difficulty quiz questions with answers. 
For each question, provide the question text and the correct answer.
Format your response as a JSON array with objects containing 'question' and 'answer' fields.

Study Note Content:
${noteContent}

Return only valid JSON in this format: [{"question": "Question text here?", "answer": "Answer text here"}]`;

    const systemPrompt = "You are an educational assistant that creates high-quality quiz questions based on study notes. Your responses should be in valid JSON format only.";
    
    try {
        const response = await callGrokApi(prompt, token, { 
            systemPrompt: systemPrompt,
            temperature: 0.7
        });
        
        // Parse the response as JSON
        return JSON.parse(response);
    } catch (error) {
        console.error("Error generating quiz with Grok 3:", error);
        throw error;
    }
}

/**
 * Generate a summary using Grok 3
 * 
 * @param {string} noteContent - The content of the note
 * @param {string} noteTitle - The title of the note
 * @param {number} maxLength - Maximum length of the summary
 * @param {string} token - The GitHub token for authentication
 * @returns {Promise<string>} - The generated summary
 */
async function generateSummaryWithGrok(noteContent, noteTitle, maxLength = 500, token) {
    // Create the prompt for Grok 3
    const prompt = `Create a concise summary of the following study note titled '${noteTitle}'. 
The summary should capture the key points and main ideas, and should be no more than ${maxLength} characters.
Make the summary clear, informative, and useful for study revision.

Study Note Content:
${noteContent}`;

    const systemPrompt = "You are an educational assistant that creates concise, informative summaries of study notes. Provide only the summary without any additional explanations or formatting.";
    
    try {
        const response = await callGrokApi(prompt, token, { 
            systemPrompt: systemPrompt,
            temperature: 0.5
        });
        
        return response;
    } catch (error) {
        console.error("Error generating summary with Grok 3:", error);
        throw error;
    }
}
