create database Auto_ecole;
use Auto_ecole;

create table Apprenant (
IdApp int primary key not null,
CIN<PERSON><PERSON> varchar(10) not null unique, 
Nom<PERSON><PERSON> varchar(10) not null,
Prenom<PERSON><PERSON> varchar(10) ,
<PERSON><PERSON><PERSON><PERSON> varchar(15) unique,
<PERSON><PERSON><PERSON> varchar(20)
);

create table CD(
IdCD int primary key not null ,
NomEditeur varchar(20) not null
); 

create table Serie(
IdSerie int primary key not null
); 

create table Seance (
IdSeance int primary key not null,
DateSeance date,
HeureSeance time ,
IdCD int not null,
Id<PERSON>erie int not null,
foreign key (IdCD) references CD(IdCD) on delete restrict on update cascade,
foreign key (IdSerie) references Serie(IdSerie) on delete restrict on update cascade
);

create table Assister (
IdApp int not null ,
IdSeance int not null ,
NoteSeance int not null
); 


create table Question (
IdQuestion int not null, 
intitule varchar(20), 
<PERSON><PERSON><PERSON> varchar(20), 
NiveauD<PERSON><PERSON>e varchar(20) ,
<PERSON> varchar(20)
);

create table OrdreQuestion(
IdQuestion int not null, 
Id<PERSON><PERSON> int not null, 
IdCD int not null,
<PERSON><PERSON><PERSON><PERSON><PERSON> varchar(20)
);

alter table Assister
add constraint pk_assis primary key (IdApp,IdSeance);

alter table Question
add constraint pk_quetion primary key (IdQuestion);

alter table OrdreQuestion
add constraint pk_quetion primary key (IdQuestion,IdSeance,IdCD);


alter table Assister
add constraint fk_app foreign key (IdApp) references Apprenant(IdApp)
on delete cascade on update cascade,
add constraint fk_sc foreign key (IdSeance) references Seance(IdSeance)
on delete cascade on update cascade;

alter table OrdreQuestion
add constraint fk_question foreign key (IdQuestion) references Question(IdQuestion)
on delete cascade on update cascade,
add constraint fk_Serie foreign key (IdSerie) references Serie(IdSerie)
on delete cascade on update cascade,
add constraint fk_CD foreign key (IdCD) references CD(IdCD)
on delete cascade on update cascade;


alter table Serie
add column NomeSerie varchar(20);


alter table Question
modify column intitule varchar(40);

alter table Apprenant
alter VilleApp set default'Safi';

alter table Apprenat 
change TeleApp GSM_App varchar(20);


alter table CD
rename to CDROM;

Set FOREIGN_KEY_CHECKS=1;

insert into Apprenant values (1, 'C000001', 'Allal', 'Alae', '066676671', 'Safi');
insert into Apprenant values (2, 'C000021', 'nfisi', 'najib', '066676626','Safi');
insert into Apprenant values (3, 'C0032', 'nadir', 'NAJAT', '066676676', 'Souira');

INSERT INTO Serie VALUES (101, 'Code 1'), (102, 'Code 2'), (103, 'Code 3');
INSERT INTO CDROM VALUES (201, 'Atlas'), (202, 'OCP'), (203, 'Sony');
INSERT INTO Seance VALUES(302, '2025-05-02', '11:00:00', 102, 202);
INSERT INTO Seance VALUES(303, '2025-05-03', '12:00:00', 103, 203);
INSERT INTO Question VALUES (401, 'Stop obligatoire ?', 'Oui', 'Facile', 'Code de la route');
INSERT INTO Question VALUES (402, 'Feu orange ?', 'Ralentir', 'Moyen', 'Signalisation');
INSERT INTO Question VALUES  (403, 'Distance sécurité ?', '2s', 'Difficile', 'Conduite');
INSERT INTO Assister VALUES (1, 301, 15), (2, 302, 14), (3, 303, 13);

INSERT INTO OrdreQuestion VALUES (401, 101, 201, 1);
INSERT INTO OrdreQuestion VALUES (402, 102, 202, 2);
INSERT INTO OrdreQuestion VALUES(403, 103, 203, 3);





delete from assister 
where IdApp=1 and IdSeance=1;

set SQL_SAFE_UPDATES=0;

update Apprenant 
set VilleApp ='El Jadida'
where prenomapp='Najib' and nomapp ='nfisi';

delete from OrdreQuestion;
drop table OrdreQuestion;



































