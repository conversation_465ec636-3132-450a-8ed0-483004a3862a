<?php
session_start();
require_once('db_connection.php');
require_once('includes/functions.php');

// Check if user is logged in
if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Check if site is in maintenance mode
check_maintenance_mode($conn);

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Check if quiz ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: ai_quiz.php?error=No quiz specified');
    exit;
}

$quiz_id = intval($_GET['id']);

// Get quiz details
$stmt = $conn->prepare("
    SELECT q.*, m.module_name
    FROM quizzes q
    JOIN modules m ON q.module_id = m.module_id
    WHERE q.quiz_id = ? AND q.user_id = ?
");
$stmt->execute([$quiz_id, $user_id]);
$quiz = $stmt->fetch();

// Check if quiz exists
if (!$quiz) {
    header('Location: ai_quiz.php?error=Quiz not found');
    exit;
}

// Get quiz questions
$stmt = $conn->prepare("
    SELECT * FROM quiz_questions 
    WHERE quiz_id = ? 
    ORDER BY question_id ASC
");
$stmt->execute([$quiz_id]);
$questions = $stmt->fetchAll();

// Handle quiz submission
$score = null;
$user_answers = [];
$feedback = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_quiz') {
    $correct_count = 0;
    
    foreach ($questions as $question) {
        $question_id = $question['question_id'];
        $user_answer = isset($_POST['answer_' . $question_id]) ? $_POST['answer_' . $question_id] : '';
        $user_answers[$question_id] = $user_answer;
        
        // Check if answer is correct
        if ($question['question_type'] == 'multiple_choice') {
            $is_correct = ($user_answer === $question['correct_answer']);
        } else {
            // For short answer, do a simple case-insensitive comparison
            // In a real app, you might want more sophisticated matching
            $is_correct = (strtolower(trim($user_answer)) === strtolower(trim($question['correct_answer'])));
        }
        
        if ($is_correct) {
            $correct_count++;
        }
        
        $feedback[$question_id] = [
            'is_correct' => $is_correct,
            'correct_answer' => $question['correct_answer']
        ];
    }
    
    // Calculate score
    $total_questions = count($questions);
    $score = $total_questions > 0 ? round(($correct_count / $total_questions) * 100) : 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - Take Quiz</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/user.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .quiz-container {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .quiz-title {
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 28px;
        }

        .quiz-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            color: var(--secondary-color);
        }

        .question-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid var(--highlight-color);
        }

        .question-text {
            font-size: 18px;
            margin-top: 0;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .options-list {
            list-style-type: none;
            padding: 0;
        }

        .option-item {
            padding: 10px 15px;
            margin-bottom: 8px;
            background-color: #fff;
            border-radius: 5px;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .option-item:hover {
            background-color: #f0f0f0;
        }

        .option-item input[type="radio"] {
            margin-right: 10px;
        }

        .short-answer-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .quiz-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .ai-icon {
            color: var(--highlight-color);
            margin-right: 5px;
        }

        .result-container {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .score {
            font-size: 36px;
            font-weight: bold;
            color: var(--primary-color);
            margin: 10px 0;
        }

        .feedback-correct {
            color: green;
        }

        .feedback-incorrect {
            color: red;
        }

        .correct-answer {
            background-color: rgba(40, 167, 69, 0.1);
            border-color: rgba(40, 167, 69, 0.3);
        }

        .incorrect-answer {
            background-color: rgba(220, 53, 69, 0.1);
            border-color: rgba(220, 53, 69, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Student Dashboard</p>
            </div>
            <div class="user-profile">
                <span class="user-name"><i class="fas fa-user-graduate"></i> <?php echo htmlspecialchars($username); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        <a href="modules.php"><i class="fas fa-book"></i> Modules</a>
                        <a href="notes.php"><i class="fas fa-sticky-note"></i> Notes</a>
                        <a href="ai_quiz.php" class="active"><i class="fas fa-robot"></i> AI Quiz</a>
                        <a href="ai_summary.php"><i class="fas fa-robot"></i> AI Summary</a>
                        <a href="ai_chat.php"><i class="fas fa-robot"></i> AI Chat</a>
                        <a href="profile.php"><i class="fas fa-user"></i> Profile</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2><i class="fas fa-robot ai-icon"></i> Take Quiz</h2>
                        <a href="ai_quiz.php" class="btn"><i class="fas fa-arrow-left"></i> Back to Quizzes</a>
                    </div>

                    <?php if ($score !== null): ?>
                        <!-- Quiz Results -->
                        <div class="result-container">
                            <h2>Quiz Results</h2>
                            <div class="score"><?php echo $score; ?>%</div>
                            <p>You answered <?php echo array_sum(array_column($feedback, 'is_correct')); ?> out of <?php echo count($questions); ?> questions correctly.</p>
                        </div>
                    <?php endif; ?>

                    <div class="quiz-container">
                        <h1 class="quiz-title"><?php echo htmlspecialchars($quiz['quiz_title']); ?></h1>

                        <div class="quiz-meta">
                            <div class="quiz-module">
                                <i class="fas fa-book"></i> <?php echo htmlspecialchars($quiz['module_name']); ?>
                            </div>
                            <div class="quiz-info">
                                <span><i class="fas fa-question-circle"></i> <?php echo count($questions); ?> questions</span>
                            </div>
                        </div>

                        <?php if (count($questions) > 0): ?>
                            <form action="take_quiz.php?id=<?php echo $quiz_id; ?>" method="post">
                                <input type="hidden" name="action" value="submit_quiz">
                                
                                <div class="questions-container">
                                    <?php foreach ($questions as $index => $question): ?>
                                        <div class="question-card <?php echo isset($feedback[$question['question_id']]) ? ($feedback[$question['question_id']]['is_correct'] ? 'correct-answer' : 'incorrect-answer') : ''; ?>">
                                            <h3 class="question-text">Question <?php echo $index + 1; ?>: <?php echo htmlspecialchars($question['question_text']); ?></h3>
                                            
                                            <?php if ($question['question_type'] == 'multiple_choice' && !empty($question['options'])): ?>
                                                <?php $options = json_decode($question['options'], true); ?>
                                                <?php if (is_array($options)): ?>
                                                    <ul class="options-list">
                                                        <?php foreach ($options as $letter => $option): ?>
                                                            <li class="option-item">
                                                                <label>
                                                                    <input type="radio" name="answer_<?php echo $question['question_id']; ?>" value="<?php echo $letter; ?>" 
                                                                        <?php echo isset($user_answers[$question['question_id']]) && $user_answers[$question['question_id']] === $letter ? 'checked' : ''; ?>
                                                                        <?php echo $score !== null ? 'disabled' : ''; ?>>
                                                                    <strong><?php echo $letter; ?>.</strong> <?php echo htmlspecialchars($option); ?>
                                                                </label>
                                                                
                                                                <?php if (isset($feedback[$question['question_id']]) && $letter === $feedback[$question['question_id']]['correct_answer']): ?>
                                                                    <i class="fas fa-check feedback-correct" style="float: right;"></i>
                                                                <?php endif; ?>
                                                            </li>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <div class="short-answer">
                                                    <input type="text" class="short-answer-input" name="answer_<?php echo $question['question_id']; ?>" 
                                                        value="<?php echo isset($user_answers[$question['question_id']]) ? htmlspecialchars($user_answers[$question['question_id']]) : ''; ?>"
                                                        placeholder="Your answer" 
                                                        <?php echo $score !== null ? 'disabled' : ''; ?>>
                                                </div>
                                                
                                                <?php if (isset($feedback[$question['question_id']])): ?>
                                                    <div class="feedback" style="margin-top: 10px;">
                                                        <strong>Correct Answer:</strong> <?php echo htmlspecialchars($feedback[$question['question_id']]['correct_answer']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <div class="quiz-actions">
                                    <?php if ($score === null): ?>
                                        <button type="submit" class="btn"><i class="fas fa-check"></i> Submit Answers</button>
                                    <?php else: ?>
                                        <a href="take_quiz.php?id=<?php echo $quiz_id; ?>" class="btn"><i class="fas fa-redo"></i> Retake Quiz</a>
                                    <?php endif; ?>
                                    <a href="view_quiz.php?id=<?php echo $quiz_id; ?>" class="btn btn-secondary"><i class="fas fa-eye"></i> View Answer Key</a>
                                </div>
                            </form>
                        <?php else: ?>
                            <p>No questions found for this quiz.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <script src="js/user.js"></script>
</body>
</html>
