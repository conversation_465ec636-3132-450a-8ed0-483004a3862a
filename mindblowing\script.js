/**
 * Enhanced StudyNotes Main Script
 * With improved animations, interactivity, and visual effects
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS (Animate On Scroll) for page elements
    initAnimations();
    
    // Setup feature cards animations and effects
    setupFeatureCards();
    
    // Configure interactive elements
    setupInteractiveElements();
    
    // Add parallax scrolling effect to welcome section
    setupParallaxEffect();
    
    // Add smooth scrolling for anchor links
    setupSmoothScrolling();
    
    // Add custom cursor effect
    setupCustomCursor();
    
    // Add welcome message in console (just for fun)
    console.log('%c Welcome to StudyNotes! ', 'background: #65350F; color: white; font-size: 1.2rem; padding: 5px; border-radius: 5px; text-shadow: 1px 1px 1px rgba(0,0,0,0.3);');
});

/**
 * Initialize animations for page elements
 */
function initAnimations() {
    // Create a staggered entrance effect for all major sections
    const sections = document.querySelectorAll('section, .welcome-section, .features, .access-section');
    
    sections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            section.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, 200 * index);
    });
    
    // Add special animation to welcome section heading
    const welcomeHeading = document.querySelector('.welcome-section h2');
    if (welcomeHeading) {
        welcomeHeading.style.opacity = '0';
        welcomeHeading.style.transform = 'translateY(-20px)';
        
        setTimeout(() => {
            welcomeHeading.style.transition = 'all 1s ease';
            welcomeHeading.style.opacity = '1';
            welcomeHeading.style.transform = 'translateY(0)';
        }, 300);
    }
}

/**
 * Setup feature cards with enhanced animations and interactions
 */
function setupFeatureCards() {
    const featureCards = document.querySelectorAll('.feature-card');
    
    featureCards.forEach((card, index) => {
        // Set initial state for animation
        card.style.opacity = '0';
        card.style.transform = 'translateY(40px)';
        
        // Add a slight delay to each card for a staggered effect
        setTimeout(() => {
            card.style.transition = 'all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 300 * index);
        
        // Add hover effect with icon animation
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transition = 'transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                icon.style.transform = 'scale(1.2) rotate(5deg)';
                
                // Add glow effect to icon
                icon.style.textShadow = '0 0 15px rgba(232, 135, 30, 0.7)';
            }
            
            // Add subtle border highlight
            this.style.borderTopWidth = '8px';
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0)';
                icon.style.textShadow = 'none';
            }
            
            // Reset border
            this.style.borderTopWidth = '5px';
        });
        
        // Add click effect
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });
    });
}

/**
 * Setup interactive elements across the site
 */
function setupInteractiveElements() {
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            ripple.style.cssText = `
                position: absolute;
                background: rgba(255, 255, 255, 0.7);
                border-radius: 50%;
                pointer-events: none;
                width: 100px;
                height: 100px;
                left: ${x - 50}px;
                top: ${y - 50}px;
                opacity: 0;
                transform: scale(0);
                transition: transform 0.8s, opacity 0.8s;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            // Trigger the ripple animation
            setTimeout(() => {
                ripple.style.transform = 'scale(4)';
                ripple.style.opacity = '0.3';
                
                setTimeout(() => {
                    ripple.remove();
                }, 800);
            }, 10);
        });
    });
    
    // Enhance access cards with advanced hover effects
    const accessCards = document.querySelectorAll('.access-card');
    
    accessCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            this.style.transform = 'translateY(-15px) scale(1.05)';
            this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.2)';
            
            // Add shine effect
            if (!this.querySelector('.shine')) {
                const shine = document.createElement('div');
                shine.classList.add('shine');
                shine.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(
                        135deg,
                        rgba(255, 255, 255, 0) 0%,
                        rgba(255, 255, 255, 0.4) 50%,
                        rgba(255, 255, 255, 0) 100%
                    );
                    z-index: 1;
                    transform: translateX(-100%);
                `;
                
                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(shine);
                
                // Animate the shine effect
                setTimeout(() => {
                    shine.style.transition = 'transform 1s';
                    shine.style.transform = 'translateX(100%)';
                    
                    setTimeout(() => {
                        shine.remove();
                    }, 1000);
                }, 10);
            }
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // Add a typewriter effect to the tagline if it exists
    const tagline = document.querySelector('.tagline');
    if (tagline) {
        const text = tagline.textContent;
        tagline.textContent = '';
        tagline.style.borderRight = '2px solid var(--highlight-color)';
        
        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                tagline.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            } else {
                tagline.style.borderRight = 'none';
            }
        };
        
        setTimeout(typeWriter, 1000);
    }
}

/**
 * Setup parallax scrolling effect
 */
function setupParallaxEffect() {
    const welcomeSection = document.querySelector('.welcome-section');
    
    if (welcomeSection) {
        // Add subtle parallax background to welcome section
        welcomeSection.style.position = 'relative';
        welcomeSection.style.overflow = 'hidden';
        
        // Create parallax background
        const parallaxBg = document.createElement('div');
        parallaxBg.style.cssText = `
            position: absolute;
            top: -50px;
            left: -50px;
            right: -50px;
            bottom: -50px;
            background: radial-gradient(circle, rgba(245, 235, 224, 0.8) 0%, rgba(245, 235, 224, 0.9) 100%);
            z-index: -1;
            opacity: 0.7;
            pointer-events: none;
        `;
        
        // Create floating elements
        for (let i = 0; i < 5; i++) {
            const floatingEl = document.createElement('div');
            const size = Math.random() * 100 + 50;
            const posX = Math.random() * 100;
            const posY = Math.random() * 100;
            const opacity = Math.random() * 0.2 + 0.05;
            
            floatingEl.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                border-radius: 50%;
                background-color: var(--accent-color);
                top: ${posY}%;
                left: ${posX}%;
                opacity: ${opacity};
                transform: scale(${Math.random() * 0.5 + 0.5});
                pointer-events: none;
            `;
            
            parallaxBg.appendChild(floatingEl);
        }
        
        welcomeSection.insertBefore(parallaxBg, welcomeSection.firstChild);
        
        // Add scroll listener for parallax effect
        window.addEventListener('scroll', function() {
            const scrollPos = window.scrollY;
            if (scrollPos < 600) {
                parallaxBg.style.transform = `translateY(${scrollPos * 0.2}px)`;
                
                // Move floating elements at different speeds
                const elements = parallaxBg.childNodes;
                elements.forEach((el, i) => {
                    const factor = (i % 3 + 1) * 0.1;
                    el.style.transform = `translate(${scrollPos * factor}px, ${scrollPos * factor * 0.5}px) scale(${Math.random() * 0.5 + 0.5})`;
                });
            }
        });
    }
}

/**
 * Setup smooth scrolling for anchor links
 */
function setupSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                // Add highlight animation to target element
                const originalBg = targetElement.style.backgroundColor;
                const originalTransition = targetElement.style.transition;
                
                // Scroll to element
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Add subtle highlight effect after scrolling
                setTimeout(() => {
                    targetElement.style.transition = 'background-color 1.5s ease';
                    targetElement.style.backgroundColor = 'rgba(232, 135, 30, 0.2)';
                    
                    setTimeout(() => {
                        targetElement.style.backgroundColor = originalBg;
                        setTimeout(() => {
                            targetElement.style.transition = originalTransition;
                        }, 1500);
                    }, 1000);
                }, 800);
            }
        });
    });
}

/**
 * Setup custom cursor effect on the page
 */
function setupCustomCursor() {
    // Check if we're on desktop (don't add on mobile/tablet)
    if (window.innerWidth > 1024) {
        // Create cursor elements
        const cursor = document.createElement('div');
        const cursorDot = document.createElement('div');
        
        cursor.classList.add('custom-cursor');
        cursorDot.classList.add('cursor-dot');
        
        // Style the outer cursor
        cursor.style.cssText = `
            position: fixed;
            width: 30px;
            height: 30px;
            border: 2px solid var(--highlight-color);
            border-radius: 50%;
            pointer-events: none;
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s, border-color 0.3s;
            z-index: 9999;
            opacity: 0.6;
            mix-blend-mode: difference;
        `;
        
        // Style the inner cursor dot
        cursorDot.style.cssText = `
            position: fixed;
            width: 5px;
            height: 5px;
            background-color: var(--primary-color);
            border-radius: 50%;
            pointer-events: none;
            transform: translate(-50%, -50%);
            z-index: 10000;
            transition: transform 0.1s;
        `;
        
        // Add cursors to the document
        document.body.appendChild(cursor);
        document.body.appendChild(cursorDot);
        
        // Track mouse movement
        document.addEventListener('mousemove', (e) => {
            // Move dot immediately
            cursorDot.style.left = `${e.clientX}px`;
            cursorDot.style.top = `${e.clientY}px`;
            
            // Move outer cursor with slight delay for trailing effect
            cursor.style.left = `${e.clientX}px`;
            cursor.style.top = `${e.clientY}px`;
        });
        
        // Special effects for interactive elements
        const interactiveElements = document.querySelectorAll('a, button, .feature-card, .access-card, input, .btn');
        
        interactiveElements.forEach(el => {
            el.addEventListener('mouseenter', () => {
                cursor.style.width = '50px';
                cursor.style.height = '50px';
                cursor.style.borderColor = 'var(--accent-color)';
                cursorDot.style.transform = 'translate(-50%, -50%) scale(1.5)';
            });
            
            el.addEventListener('mouseleave', () => {
                cursor.style.width = '30px';
                cursor.style.height = '30px';
                cursor.style.borderColor = 'var(--highlight-color)';
                cursorDot.style.transform = 'translate(-50%, -50%) scale(1)';
            });
            
            // Hide default cursor on these elements
            el.style.cursor = 'none';
        });
        
        // Hide cursors when mouse leaves the window
        document.addEventListener('mouseout', (e) => {
            if (e.relatedTarget === null) {
                cursor.style.opacity = '0';
                cursorDot.style.opacity = '0';
            }
        });
        
        document.addEventListener('mouseover', () => {
            cursor.style.opacity = '0.6';
            cursorDot.style.opacity = '1';
        });
        
        // Hide default cursor
        document.body.style.cursor = 'none';
    }
}