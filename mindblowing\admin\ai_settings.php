<?php
session_start();
require_once('../db_connection.php');

// Check if user is logged in as admin
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header('Location: login.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Check if ai_settings table exists, if not create it
        $stmt = $conn->prepare("
            CREATE TABLE IF NOT EXISTS ai_settings (
                setting_id INT AUTO_INCREMENT PRIMARY KEY,
                setting_name VARCHAR(50) NOT NULL UNIQUE,
                setting_value TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        $stmt->execute();

        // Update or insert settings
        $settings = [
            'openai_api_key' => $_POST['openai_api_key'],
            'default_quiz_questions' => $_POST['default_quiz_questions'],
            'default_quiz_difficulty' => $_POST['default_quiz_difficulty'],
            'default_quiz_type' => $_POST['default_quiz_type'],
            'enable_ai_features' => isset($_POST['enable_ai_features']) ? 1 : 0
        ];

        foreach ($settings as $name => $value) {
            // Check if setting exists
            $stmt = $conn->prepare("SELECT COUNT(*) FROM ai_settings WHERE setting_name = ?");
            $stmt->execute([$name]);
            $exists = $stmt->fetchColumn() > 0;

            if ($exists) {
                // Update existing setting
                $stmt = $conn->prepare("UPDATE ai_settings SET setting_value = ? WHERE setting_name = ?");
                $stmt->execute([$value, $name]);
            } else {
                // Insert new setting
                $stmt = $conn->prepare("INSERT INTO ai_settings (setting_name, setting_value) VALUES (?, ?)");
                $stmt->execute([$name, $value]);
            }
        }

        // Update the DeepSeek config file
        $config_file = '../includes/openai_config.php';
        if (file_exists($config_file)) {
            $config_content = file_get_contents($config_file);
            $config_content = preg_replace(
                "/define\('AI_API_KEY', '.*?'\);/",
                "define('AI_API_KEY', '{$settings['openai_api_key']}');",
                $config_content
            );
            file_put_contents($config_file, $config_content);
        }

        $success = "AI settings updated successfully";
    } catch (PDOException $e) {
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
try {
    // Check if ai_settings table exists
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'ai_settings'
    ");
    $stmt->execute();
    $table_exists = $stmt->fetchColumn() > 0;

    $settings = [
        'openai_api_key' => '',
        'default_quiz_questions' => '5',
        'default_quiz_difficulty' => 'medium',
        'default_quiz_type' => 'multiple_choice',
        'enable_ai_features' => 1
    ];

    if ($table_exists) {
        // Get settings from database
        $stmt = $conn->prepare("SELECT setting_name, setting_value FROM ai_settings");
        $stmt->execute();
        $db_settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        // Merge with defaults
        $settings = array_merge($settings, $db_settings);
    }
} catch (PDOException $e) {
    $error = "Error retrieving settings: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - AI Settings</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .api-key-field {
            font-family: monospace;
            letter-spacing: 1px;
        }
        .settings-card {
            margin-bottom: 20px;
        }
        .settings-card h3 {
            margin-bottom: 15px;
            color: var(--primary-color);
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .help-text {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Admin Dashboard</p>
            </div>
            <div class="admin-profile">
                <span class="admin-name"><i class="fas fa-user-shield"></i> <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-users"></i> User Management</a>
                        <a href="statistics.php"><i class="fas fa-chart-line"></i> Statistics</a>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                        <a href="ai_settings.php" class="active"><i class="fas fa-robot"></i> AI Settings</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2><i class="fas fa-robot"></i> AI Settings</h2>
                    </div>

                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <div class="card">
                        <div class="card-body">
                            <form action="ai_settings.php" method="post">
                                <div class="settings-card">
                                    <h3><i class="fas fa-key"></i> API Configuration</h3>
                                    <div class="form-group">
                                        <label for="openai_api_key">DeepSeek API Key</label>
                                        <input type="text" id="openai_api_key" name="openai_api_key" value="<?php echo htmlspecialchars($settings['openai_api_key']); ?>" class="api-key-field" required>
                                        <p class="help-text">Your DeepSeek API key. Keep this secure.</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="enable_ai_features" <?php echo $settings['enable_ai_features'] ? 'checked' : ''; ?>>
                                            Enable AI Features
                                        </label>
                                        <p class="help-text">When disabled, all AI features will be unavailable to users.</p>
                                    </div>
                                </div>

                                <div class="settings-card">
                                    <h3><i class="fas fa-question-circle"></i> Quiz Generation Defaults</h3>
                                    <div class="form-group">
                                        <label for="default_quiz_questions">Default Number of Questions</label>
                                        <input type="number" id="default_quiz_questions" name="default_quiz_questions" value="<?php echo htmlspecialchars($settings['default_quiz_questions']); ?>" min="1" max="20" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="default_quiz_difficulty">Default Difficulty</label>
                                        <select id="default_quiz_difficulty" name="default_quiz_difficulty" required>
                                            <option value="easy" <?php echo $settings['default_quiz_difficulty'] == 'easy' ? 'selected' : ''; ?>>Easy</option>
                                            <option value="medium" <?php echo $settings['default_quiz_difficulty'] == 'medium' ? 'selected' : ''; ?>>Medium</option>
                                            <option value="hard" <?php echo $settings['default_quiz_difficulty'] == 'hard' ? 'selected' : ''; ?>>Hard</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="default_quiz_type">Default Question Type</label>
                                        <select id="default_quiz_type" name="default_quiz_type" required>
                                            <option value="multiple_choice" <?php echo $settings['default_quiz_type'] == 'multiple_choice' ? 'selected' : ''; ?>>Multiple Choice</option>
                                            <option value="short_answer" <?php echo $settings['default_quiz_type'] == 'short_answer' ? 'selected' : ''; ?>>Short Answer</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-buttons">
                                    <button type="submit" class="btn">Save Settings</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <script src="js/admin.js"></script>
</body>
</html>
