<?php
session_start();
require_once('db_connection.php');
require_once('includes/functions.php');

// Check if user is logged in
if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Check if site is in maintenance mode
check_maintenance_mode($conn);

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Get all modules for this user
$stmt = $conn->prepare("SELECT * FROM modules WHERE user_id = ? ORDER BY module_name ASC");
$stmt->execute([$user_id]);
$modules = $stmt->fetchAll();

// Handle module actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add new module
    if (isset($_POST['action']) && $_POST['action'] === 'add_module') {
        $module_name = trim($_POST['module_name']);
        $module_description = trim($_POST['module_description']);

        // Validate input
        if (!empty($module_name)) {
            try {
                $stmt = $conn->prepare("INSERT INTO modules (module_name, module_description, user_id) VALUES (?, ?, ?)");
                $stmt->execute([$module_name, $module_description, $user_id]);

                // Redirect to refresh page
                header('Location: modules.php?success=Module added successfully');
                exit;
            } catch (PDOException $e) {
                $error = "Error adding module: " . $e->getMessage();
            }
        } else {
            $error = "Module name is required";
        }
    }

    // Delete module
    if (isset($_POST['action']) && $_POST['action'] === 'delete_module') {
        $module_id = $_POST['module_id'];

        try {
            $stmt = $conn->prepare("DELETE FROM modules WHERE module_id = ? AND user_id = ?");
            $stmt->execute([$module_id, $user_id]);

            // Redirect to refresh page
            header('Location: modules.php?success=Module deleted successfully');
            exit;
        } catch (PDOException $e) {
            $error = "Error deleting module: " . $e->getMessage();
        }
    }

    // Edit module
    if (isset($_POST['action']) && $_POST['action'] === 'edit_module') {
        $module_id = $_POST['module_id'];
        $module_name = trim($_POST['module_name']);
        $module_description = trim($_POST['module_description']);

        // Validate input
        if (!empty($module_name)) {
            try {
                $stmt = $conn->prepare("UPDATE modules SET module_name = ?, module_description = ? WHERE module_id = ? AND user_id = ?");
                $stmt->execute([$module_name, $module_description, $module_id, $user_id]);

                // Redirect to refresh page
                header('Location: modules.php?success=Module updated successfully');
                exit;
            } catch (PDOException $e) {
                $error = "Error updating module: " . $e->getMessage();
            }
        } else {
            $error = "Module name is required";
        }
    }
}

// Get success or error messages
$success = isset($_GET['success']) ? $_GET['success'] : '';
$error = isset($error) ? $error : '';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - Modules</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/user.css">
    <link rel="stylesheet" href="css/module-search.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Your Study Modules</p>
            </div>
            <div class="user-profile">
                <span class="user-name"><i class="fas fa-user-graduate"></i> <?php echo htmlspecialchars($username); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        <a href="modules.php" class="active"><i class="fas fa-book"></i> Modules</a>
                        <a href="notes.php"><i class="fas fa-sticky-note"></i> Notes</a>
                        <a href="ai_quiz.php"><i class="fas fa-robot"></i> AI Quiz</a>
                        <a href="ai_summary.php"><i class="fas fa-robot"></i> AI Summary</a>
                        <a href="ai_chat.php"><i class="fas fa-robot"></i> AI Chat</a>
                        <a href="profile.php"><i class="fas fa-user"></i> Profile</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2>Your Modules</h2>
                        <button id="addModuleBtn" class="btn"><i class="fas fa-plus"></i> Add New Module</button>
                    </div>

                    <?php if (!empty($success)): ?>
                        <div class="success-message">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error)): ?>
                        <div class="error-message">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Enhanced Module Search and Filter -->
                    <div class="filter-container">
                        <div class="search-box" id="searchBoxContainer">
                            <input type="text" id="moduleSearch" placeholder="Search your modules...">
                            <i class="fas fa-search search-icon"></i>
                            <span class="clear-search" id="clearSearchBtn"><i class="fas fa-times"></i></span>
                            <span class="search-results-count" id="searchResultsCount"></span>
                        </div>
                        <div class="sort-options">
                            <label for="sortModules"><i class="fas fa-sort"></i> Sort by:</label>
                            <select id="sortModules">
                                <option value="name-asc">Name (A-Z)</option>
                                <option value="name-desc">Name (Z-A)</option>
                                <option value="date-newest">Date (Newest)</option>
                                <option value="date-oldest">Date (Oldest)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Modules List -->
                    <div class="card">
                        <div class="card-body">
                            <?php if (count($modules) > 0): ?>
                                <div class="modules-grid" id="modulesGrid">
                                    <?php foreach ($modules as $module): ?>
                                        <div class="module-card" data-name="<?php echo htmlspecialchars(strtolower($module['module_name'])); ?>" data-date="<?php echo strtotime($module['created_at']); ?>">
                                            <div class="module-header">
                                                <h4><?php echo htmlspecialchars($module['module_name']); ?></h4>
                                            </div>
                                            <div class="module-description">
                                                <?php echo !empty($module['module_description']) ? htmlspecialchars($module['module_description']) : 'No description provided.'; ?>
                                            </div>
                                            <div class="module-meta">
                                                <span class="module-date"><i class="fas fa-calendar-alt"></i> Created: <?php echo date('M d, Y', strtotime($module['created_at'])); ?></span>

                                                <?php
                                                // Get note count for this module
                                                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM notes WHERE module_id = ? AND user_id = ?");
                                                $stmt->execute([$module['module_id'], $user_id]);
                                                $note_count = $stmt->fetch()['count'];
                                                ?>

                                                <span class="note-count"><i class="fas fa-sticky-note"></i> <?php echo $note_count; ?> Notes</span>
                                            </div>
                                            <div class="module-footer">
                                                <a href="notes.php?module=<?php echo $module['module_id']; ?>" class="btn-sm"><i class="fas fa-sticky-note"></i> View Notes</a>
                                                <div class="module-actions">
                                                    <button class="btn-icon edit-module-btn" data-id="<?php echo $module['module_id']; ?>" data-name="<?php echo htmlspecialchars($module['module_name']); ?>" data-description="<?php echo htmlspecialchars($module['module_description']); ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn-icon delete-module-btn" data-id="<?php echo $module['module_id']; ?>" data-name="<?php echo htmlspecialchars($module['module_name']); ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="no-data">
                                    <p>You haven't created any modules yet. Click the "Add New Module" button to get started.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <!-- Add Module Modal -->
    <div id="addModuleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Module</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form action="modules.php" method="post">
                    <input type="hidden" name="action" value="add_module">
                    <div class="form-group">
                        <label for="module_name">Module Name</label>
                        <input type="text" id="module_name" name="module_name" required>
                    </div>
                    <div class="form-group">
                        <label for="module_description">Description (Optional)</label>
                        <textarea id="module_description" name="module_description" rows="4"></textarea>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn">Create Module</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Module Modal -->
    <div id="editModuleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Module</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form action="modules.php" method="post">
                    <input type="hidden" name="action" value="edit_module">
                    <input type="hidden" name="module_id" id="edit_module_id">
                    <div class="form-group">
                        <label for="edit_module_name">Module Name</label>
                        <input type="text" id="edit_module_name" name="module_name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_module_description">Description (Optional)</label>
                        <textarea id="edit_module_description" name="module_description" rows="4"></textarea>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn">Update Module</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Module Modal -->
    <div id="deleteModuleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Delete Module</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete module <strong id="delete_module_name"></strong>?</p>
                <p class="warning">This action cannot be undone. All notes in this module will be deleted.</p>
                <form action="modules.php" method="post">
                    <input type="hidden" name="action" value="delete_module">
                    <input type="hidden" name="module_id" id="delete_module_id">
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Module</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/user.js"></script>
    <script>
        // Enhanced Module search and filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('moduleSearch');
            const searchBoxContainer = document.getElementById('searchBoxContainer');
            const searchResultsCount = document.getElementById('searchResultsCount');
            const sortSelect = document.getElementById('sortModules');
            const modulesGrid = document.getElementById('modulesGrid');
            const moduleCards = document.querySelectorAll('.module-card');

            // Add animation to the filter container
            const filterContainer = document.querySelector('.filter-container');
            if (filterContainer) {
                setTimeout(() => {
                    filterContainer.style.opacity = '0';
                    filterContainer.style.transform = 'translateY(-10px)';

                    // Force reflow
                    void filterContainer.offsetWidth;

                    filterContainer.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    filterContainer.style.opacity = '1';
                    filterContainer.style.transform = 'translateY(0)';
                }, 100);
            }

            if (searchInput && sortSelect && modulesGrid) {
                // Focus the search input with a slight delay for better UX
                setTimeout(() => {
                    searchInput.focus();
                }, 500);

                // Clear search button functionality
                const clearSearchBtn = document.getElementById('clearSearchBtn');

                const updateSearchState = () => {
                    if (searchInput.value.length > 0) {
                        searchBoxContainer.classList.add('has-value');
                        searchBoxContainer.classList.add('searching');
                    } else {
                        searchBoxContainer.classList.remove('has-value');
                        setTimeout(() => {
                            if (searchInput.value.length === 0) {
                                searchBoxContainer.classList.remove('searching');
                            }
                        }, 300);
                    }
                };

                searchInput.addEventListener('input', updateSearchState);
                searchInput.addEventListener('focus', function() {
                    if (this.value.length > 0) {
                        searchBoxContainer.classList.add('searching');
                    }
                });

                // Clear search when clicking the X
                clearSearchBtn.addEventListener('click', function() {
                    searchInput.value = '';
                    searchInput.dispatchEvent(new Event('input'));
                    searchInput.focus();
                });

                // Enhanced search functionality with debounce
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);

                    searchTimeout = setTimeout(() => {
                        const searchTerm = this.value.toLowerCase().trim();
                        let visibleCount = 0;

                        // Add subtle animation to cards
                        moduleCards.forEach(card => {
                            const moduleName = card.getAttribute('data-name');
                            const moduleDescription = card.querySelector('.module-description').textContent.toLowerCase();
                            const isVisible = moduleName.includes(searchTerm) || moduleDescription.includes(searchTerm);

                            if (isVisible) {
                                visibleCount++;
                                card.style.display = 'block';
                                card.style.opacity = '0';
                                card.style.transform = 'scale(0.95)';

                                setTimeout(() => {
                                    card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                                    card.style.opacity = '1';
                                    card.style.transform = 'scale(1)';
                                }, 50 * visibleCount); // Staggered animation
                            } else {
                                card.style.display = 'none';
                            }
                        });

                        // Update search results count and UI
                        if (searchTerm !== '') {
                            // Update the page title to show we're in search mode
                            const pageHeader = document.querySelector('.page-header h2');
                            if (pageHeader) {
                                if (!pageHeader.getAttribute('data-original')) {
                                    pageHeader.setAttribute('data-original', pageHeader.textContent);
                                }

                                // Only update if not already in search mode
                                if (!pageHeader.classList.contains('searching')) {
                                    pageHeader.classList.add('searching');
                                }
                            }

                            // Update search results count
                            searchResultsCount.textContent = `${visibleCount} result${visibleCount !== 1 ? 's' : ''} found`;
                            searchResultsCount.classList.add('visible');
                        } else {
                            // Restore original title
                            const pageHeader = document.querySelector('.page-header h2');
                            if (pageHeader && pageHeader.getAttribute('data-original')) {
                                pageHeader.classList.remove('searching');
                            }

                            searchResultsCount.classList.remove('visible');
                        }

                        // Show message if no results
                        if (visibleCount === 0 && searchTerm !== '') {
                            let noResults = document.querySelector('.no-search-results');
                            if (!noResults) {
                                noResults = document.createElement('div');
                                noResults.className = 'no-search-results';
                                noResults.innerHTML = `
                                    <p><i class="fas fa-search"></i> No modules found matching "${searchTerm}"</p>
                                    <p class="suggestion">Try a different search term or <a href="#" id="clearSearch">clear search</a></p>
                                `;
                                modulesGrid.appendChild(noResults);

                                // Add clear search functionality
                                document.getElementById('clearSearch').addEventListener('click', function(e) {
                                    e.preventDefault();
                                    searchInput.value = '';
                                    searchInput.dispatchEvent(new Event('input'));
                                    searchInput.focus();
                                });
                            }
                        } else {
                            const noResults = document.querySelector('.no-search-results');
                            if (noResults) {
                                noResults.remove();
                            }
                        }
                    }, 300); // Debounce delay
                });

                // Enhanced sort functionality with animation
                sortSelect.addEventListener('change', function() {
                    const sortValue = this.value;
                    const cards = Array.from(moduleCards);

                    // Add sorting animation
                    cards.forEach(card => {
                        card.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
                        card.style.opacity = '0.5';
                        card.style.transform = 'translateY(10px)';
                    });

                    setTimeout(() => {
                        cards.sort((a, b) => {
                            if (sortValue === 'name-asc') {
                                return a.getAttribute('data-name').localeCompare(b.getAttribute('data-name'));
                            } else if (sortValue === 'name-desc') {
                                return b.getAttribute('data-name').localeCompare(a.getAttribute('data-name'));
                            } else if (sortValue === 'date-newest') {
                                return b.getAttribute('data-date') - a.getAttribute('data-date');
                            } else if (sortValue === 'date-oldest') {
                                return a.getAttribute('data-date') - b.getAttribute('data-date');
                            }
                        });

                        // Reorder cards with staggered animation
                        cards.forEach((card, index) => {
                            modulesGrid.appendChild(card);

                            setTimeout(() => {
                                card.style.opacity = '1';
                                card.style.transform = 'translateY(0)';
                            }, 50 * index);
                        });
                    }, 300);
                });

                // Initial sort (if needed)
                if (sortSelect.value) {
                    sortSelect.dispatchEvent(new Event('change'));
                }
            }
        });
    </script>
</body>
</html>
