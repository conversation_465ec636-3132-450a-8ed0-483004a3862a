<?php
session_start();
require_once('../db_connection.php');

// Check if user is logged in as admin
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header('Location: login.php');
    exit;
}

// Handle form submissions
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Update admin password
    if (isset($_POST['action']) && $_POST['action'] === 'update_password') {
        $current_password = trim($_POST['current_password']);
        $new_password = trim($_POST['new_password']);
        $confirm_password = trim($_POST['confirm_password']);

        // Get current admin info
        $admin_id = $_SESSION['admin_id'];
        $stmt = $conn->prepare("SELECT password FROM admins WHERE admin_id = ?");
        $stmt->execute([$admin_id]);
        $admin = $stmt->fetch();

        // Validate input
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error = "All password fields are required";
        } elseif ($current_password !== $admin['password']) { // In a real app, use password_verify()
            $error = "Current password is incorrect";
        } elseif ($new_password !== $confirm_password) {
            $error = "New passwords do not match";
        } elseif (strlen($new_password) < 6) {
            $error = "New password must be at least 6 characters long";
        } else {
            try {
                // Update password
                $stmt = $conn->prepare("UPDATE admins SET password = ? WHERE admin_id = ?");
                $stmt->execute([$new_password, $admin_id]); // In a real app, use password_hash()

                $success = "Password updated successfully";
            } catch (PDOException $e) {
                $error = "Error updating password: " . $e->getMessage();
            }
        }
    }

    // Update system settings
    if (isset($_POST['action']) && $_POST['action'] === 'update_settings') {
        $site_name = trim($_POST['site_name']);
        $site_description = trim($_POST['site_description']);
        $maintenance_mode = isset($_POST['maintenance_mode']) ? 1 : 0;

        try {
            // Check if settings table exists, if not create it
            $stmt = $conn->prepare("
                CREATE TABLE IF NOT EXISTS settings (
                    setting_id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_name VARCHAR(50) NOT NULL UNIQUE,
                    setting_value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            $stmt->execute();

            // Update or insert settings
            $settings = [
                'site_name' => $site_name,
                'site_description' => $site_description,
                'maintenance_mode' => $maintenance_mode
            ];

            foreach ($settings as $name => $value) {
                // Check if setting exists
                $stmt = $conn->prepare("SELECT COUNT(*) FROM settings WHERE setting_name = ?");
                $stmt->execute([$name]);
                $exists = $stmt->fetchColumn() > 0;

                if ($exists) {
                    // Update existing setting
                    $stmt = $conn->prepare("UPDATE settings SET setting_value = ? WHERE setting_name = ?");
                    $stmt->execute([$value, $name]);
                } else {
                    // Insert new setting
                    $stmt = $conn->prepare("INSERT INTO settings (setting_name, setting_value) VALUES (?, ?)");
                    $stmt->execute([$name, $value]);
                }
            }

            $success = "System settings updated successfully";
        } catch (PDOException $e) {
            $error = "Error updating settings: " . $e->getMessage();
        }
    }
}

// Get current settings
try {
    // Check if settings table exists
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'settings'
    ");
    $stmt->execute();
    $table_exists = $stmt->fetchColumn() > 0;

    $settings = [
        'site_name' => 'StudyNotes',
        'site_description' => 'A platform for organizing study notes and modules',
        'maintenance_mode' => 0
    ];

    if ($table_exists) {
        // Get settings from database
        $stmt = $conn->prepare("SELECT setting_name, setting_value FROM settings");
        $stmt->execute();
        $db_settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        // Merge with defaults
        $settings = array_merge($settings, $db_settings);
    }
} catch (PDOException $e) {
    $error = "Error retrieving settings: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - Admin Settings</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary-color: #65350F;
            --secondary-color: #A67B5B;
            --highlight-color: #E8871E;
        }

        .settings-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .settings-card {
            background-color: var(--card-color);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .settings-header {
            padding: 1.5rem;
            background-color: rgba(101, 53, 15, 0.05);
            border-bottom: 1px solid #eee;
        }

        .settings-header h3 {
            margin: 0;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .settings-header h3 i {
            margin-right: 0.5rem;
        }

        .settings-body {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--primary-color);
        }

        .form-group input[type="text"],
        .form-group input[type="password"],
        .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input[type="text"]:focus,
        .form-group input[type="password"]:focus,
        .form-group textarea:focus {
            border-color: var(--highlight-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(232, 135, 30, 0.1);
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-group .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .form-group .checkbox-label input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .form-buttons {
            display: flex;
            justify-content: flex-end;
            margin-top: 1.5rem;
        }

        .password-strength {
            margin-top: 0.5rem;
            height: 5px;
            background-color: #eee;
            border-radius: 5px;
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: width 0.3s, background-color 0.3s;
        }

        .password-feedback {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--secondary-color);
        }

        .system-info {
            background-color: var(--card-color);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .system-info h3 {
            margin-top: 0;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .info-item {
            background-color: rgba(101, 53, 15, 0.05);
            padding: 1rem;
            border-radius: 5px;
        }

        .info-item h4 {
            margin: 0 0 0.5rem;
            color: var(--primary-color);
            font-size: 0.9rem;
        }

        .info-item p {
            margin: 0;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .settings-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Admin Dashboard</p>
            </div>
            <div class="admin-profile">
                <span class="admin-name"><i class="fas fa-user-shield"></i> <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-users"></i> User Management</a>
                        <a href="statistics.php"><i class="fas fa-chart-line"></i> Statistics</a>
                        <a href="settings.php" class="active"><i class="fas fa-cog"></i> Settings</a>
                        <a href="ai_settings.php"><i class="fas fa-robot"></i> AI Settings</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2>System Settings</h2>
                    </div>

                    <?php if (!empty($success)): ?>
                        <div class="success-message">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error)): ?>
                        <div class="error-message">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <div class="settings-container">
                        <!-- Admin Password Settings -->
                        <div class="settings-card">
                            <div class="settings-header">
                                <h3><i class="fas fa-lock"></i> Admin Security</h3>
                            </div>
                            <div class="settings-body">
                                <form action="settings.php" method="post" id="passwordForm">
                                    <input type="hidden" name="action" value="update_password">

                                    <div class="form-group">
                                        <label for="current_password">Current Password</label>
                                        <input type="password" id="current_password" name="current_password" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="new_password">New Password</label>
                                        <input type="password" id="new_password" name="new_password" required>
                                        <div class="password-strength">
                                            <div class="password-strength-bar" id="passwordStrengthBar"></div>
                                        </div>
                                        <div class="password-feedback" id="passwordFeedback"></div>
                                    </div>

                                    <div class="form-group">
                                        <label for="confirm_password">Confirm New Password</label>
                                        <input type="password" id="confirm_password" name="confirm_password" required>
                                    </div>

                                    <div class="form-buttons">
                                        <button type="submit" class="btn">Update Password</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- System Settings -->
                        <div class="settings-card">
                            <div class="settings-header">
                                <h3><i class="fas fa-sliders-h"></i> System Configuration</h3>
                            </div>
                            <div class="settings-body">
                                <form action="settings.php" method="post">
                                    <input type="hidden" name="action" value="update_settings">

                                    <div class="form-group">
                                        <label for="site_name">Site Name</label>
                                        <input type="text" id="site_name" name="site_name" value="<?php echo htmlspecialchars($settings['site_name']); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="site_description">Site Description</label>
                                        <textarea id="site_description" name="site_description"><?php echo htmlspecialchars($settings['site_description']); ?></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="maintenance_mode" <?php echo $settings['maintenance_mode'] ? 'checked' : ''; ?>>
                                            Enable Maintenance Mode
                                        </label>
                                        <p class="help-text">When enabled, only administrators can access the site.</p>
                                    </div>

                                    <div class="form-buttons">
                                        <button type="submit" class="btn">Save Settings</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="system-info">
                        <h3><i class="fas fa-info-circle"></i> System Information</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <h4>PHP Version</h4>
                                <p><?php echo phpversion(); ?></p>
                            </div>
                            <div class="info-item">
                                <h4>Database</h4>
                                <p>MySQL <?php echo $conn->getAttribute(PDO::ATTR_SERVER_VERSION); ?></p>
                            </div>
                            <div class="info-item">
                                <h4>Server</h4>
                                <p><?php echo $_SERVER['SERVER_SOFTWARE']; ?></p>
                            </div>
                            <div class="info-item">
                                <h4>Last Settings Update</h4>
                                <p><?php echo date('M d, Y H:i'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <script src="js/admin-animations.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Password strength meter
            const passwordInput = document.getElementById('new_password');
            const strengthBar = document.getElementById('passwordStrengthBar');
            const feedback = document.getElementById('passwordFeedback');
            const confirmInput = document.getElementById('confirm_password');

            if (passwordInput && strengthBar && feedback) {
                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    let message = '';

                    if (password.length >= 8) strength += 25;
                    if (password.match(/[a-z]+/)) strength += 25;
                    if (password.match(/[A-Z]+/)) strength += 25;
                    if (password.match(/[0-9]+/)) strength += 25;

                    strengthBar.style.width = strength + '%';

                    if (strength <= 25) {
                        strengthBar.style.backgroundColor = '#e74c3c';
                        message = 'Weak password';
                    } else if (strength <= 50) {
                        strengthBar.style.backgroundColor = '#f39c12';
                        message = 'Moderate password';
                    } else if (strength <= 75) {
                        strengthBar.style.backgroundColor = '#3498db';
                        message = 'Good password';
                    } else {
                        strengthBar.style.backgroundColor = '#2ecc71';
                        message = 'Strong password';
                    }

                    feedback.textContent = message;
                });
            }

            // Password confirmation validation
            if (confirmInput && passwordInput) {
                confirmInput.addEventListener('input', function() {
                    if (this.value === passwordInput.value) {
                        this.style.borderColor = '#2ecc71';
                    } else {
                        this.style.borderColor = '#e74c3c';
                    }
                });
            }

            // Form validation
            const passwordForm = document.getElementById('passwordForm');
            if (passwordForm) {
                passwordForm.addEventListener('submit', function(e) {
                    if (confirmInput.value !== passwordInput.value) {
                        e.preventDefault();
                        alert('Passwords do not match');
                    }
                });
            }
        });
    </script>
</body>
</html>
