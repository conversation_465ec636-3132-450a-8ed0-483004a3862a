<?php
/**
 * DeepSeek API Handler
 *
 * This class handles interactions with the DeepSeek API for various AI features.
 */

require_once('openai_config.php');

class OpenAIHandler {
    private $api_key;
    private $api_endpoint;
    private $model;
    private $default_params;

    /**
     * Constructor
     */
    public function __construct() {
        $this->api_key = AI_API_KEY;
        $this->api_endpoint = AI_API_ENDPOINT;
        $this->model = AI_MODEL;
        $this->default_params = getDefaultAIParams();
    }

    /**
     * Make a request to the DeepSeek API
     *
     * @param string $endpoint The API endpoint to call
     * @param array $data The data to send
     * @return array|null The response data or null on error
     */
    private function makeRequest($endpoint, $data) {
        $url = $this->api_endpoint . $endpoint;

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->api_key
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($http_code >= 200 && $http_code < 300) {
            return json_decode($response, true);
        } else {
            error_log("DeepSeek API Error: " . $response);
            return null;
        }
    }

    /**
     * Generate text using the DeepSeek API
     *
     * @param array $messages The messages to send
     * @param array $params Additional parameters to override defaults
     * @return string|null The generated text or null on error
     */
    public function generateText($messages, $params = []) {
        // Merge default parameters with provided parameters
        $request_params = array_merge($this->default_params, $params);
        $request_params['messages'] = $messages;

        $response = $this->makeRequest('/chat/completions', $request_params);

        if ($response && isset($response['choices'][0]['message']['content'])) {
            return $response['choices'][0]['message']['content'];
        }

        return null;
    }

    /**
     * Generate quiz questions based on note content
     *
     * @param string $content The note content
     * @param int $num_questions Number of questions to generate
     * @param string $difficulty Difficulty level (easy, medium, hard)
     * @param string $question_type Type of questions (multiple_choice, short_answer)
     * @return array|null Array of questions or null on error
     */
    public function generateQuizQuestions($content, $num_questions = 5, $difficulty = 'medium', $question_type = 'multiple_choice') {
        $system_prompt = "You are an expert quiz creator. Create {$num_questions} {$difficulty} {$question_type} questions based on the following content. ";

        if ($question_type == 'multiple_choice') {
            $system_prompt .= "For each question, provide 4 options (A, B, C, D) with one correct answer. Format your response as a JSON array with each question having 'question_text', 'options' (array of 4 options), and 'correct_answer' (the letter of the correct option).";
        } else {
            $system_prompt .= "For each question, provide the correct answer. Format your response as a JSON array with each question having 'question_text' and 'correct_answer'.";
        }

        $messages = [
            ['role' => 'system', 'content' => $system_prompt],
            ['role' => 'user', 'content' => $content]
        ];

        $response = $this->generateText($messages, [
            'temperature' => 0.7,
            'max_tokens' => 2000
        ]);

        if ($response) {
            // Extract JSON from response
            $json_start = strpos($response, '[');
            $json_end = strrpos($response, ']');

            if ($json_start !== false && $json_end !== false) {
                $json_str = substr($response, $json_start, $json_end - $json_start + 1);
                $questions = json_decode($json_str, true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return $questions;
                }
            }

            // Fallback: try to parse the entire response as JSON
            $questions = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $questions;
            }
        }

        return null;
    }

    /**
     * Generate a summary of note content
     *
     * @param string $content The note content to summarize
     * @param int $max_length Maximum length of summary (approximate word count)
     * @return string|null The generated summary or null on error
     */
    public function generateSummary($content, $max_length = 200) {
        $system_prompt = "You are an expert at creating concise, well-structured summaries of academic content. Create a summary of the following text in approximately {$max_length} words. Preserve important terminology and maintain academic accuracy.";

        $messages = [
            ['role' => 'system', 'content' => $system_prompt],
            ['role' => 'user', 'content' => $content]
        ];

        return $this->generateText($messages, [
            'temperature' => 0.5,
            'max_tokens' => 1000
        ]);
    }

    /**
     * Generate a chatbot response based on user query and context
     *
     * @param string $query The user's question
     * @param array $context Array of relevant content from notes/modules
     * @param array $chat_history Previous messages in the conversation
     * @return string|null The generated response or null on error
     */
    public function generateChatResponse($query, $context = [], $chat_history = []) {
        $system_prompt = "You are a helpful study assistant chatbot that helps students understand their study materials. Answer questions based on the provided context from their notes and modules. If you don't know the answer based on the provided context, say so rather than making up information.";

        // Format context as a string
        $context_str = "";
        foreach ($context as $item) {
            $context_str .= "--- {$item['title']} ---\n{$item['content']}\n\n";
        }

        $messages = [
            ['role' => 'system', 'content' => $system_prompt]
        ];

        // Add chat history
        foreach ($chat_history as $message) {
            $messages[] = [
                'role' => $message['role'],
                'content' => $message['content']
            ];
        }

        // Add context and current query
        $messages[] = [
            'role' => 'user',
            'content' => "Context information:\n{$context_str}\n\nMy question is: {$query}"
        ];

        return $this->generateText($messages, [
            'temperature' => 0.7,
            'max_tokens' => 1000
        ]);
    }
}
