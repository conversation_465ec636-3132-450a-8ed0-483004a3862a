/**
 * Enhanced Edit User Modal Functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Password toggle functionality
    const togglePasswordBtn = document.getElementById('toggleEditPassword');
    const passwordInput = document.getElementById('edit_password');
    
    if (togglePasswordBtn && passwordInput) {
        togglePasswordBtn.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            // Toggle icon
            const icon = this.querySelector('i');
            if (type === 'text') {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
    
    // Password strength meter
    const strengthBar = document.getElementById('passwordStrengthBar');
    const strengthText = document.getElementById('passwordStrengthText');
    
    if (passwordInput && strengthBar && strengthText) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            
            if (password.length === 0) {
                strengthBar.style.width = '0';
                strengthBar.style.backgroundColor = '#eee';
                strengthText.textContent = '';
                return;
            }
            
            // Calculate password strength
            let strength = 0;
            let feedback = '';
            
            // Length check
            if (password.length >= 8) {
                strength += 25;
            }
            
            // Lowercase letters check
            if (password.match(/[a-z]+/)) {
                strength += 25;
            }
            
            // Uppercase letters check
            if (password.match(/[A-Z]+/)) {
                strength += 25;
            }
            
            // Numbers and special characters check
            if (password.match(/[0-9]+/) || password.match(/[^a-zA-Z0-9]+/)) {
                strength += 25;
            }
            
            // Update strength bar
            strengthBar.style.width = strength + '%';
            
            // Set color and feedback based on strength
            if (strength <= 25) {
                strengthBar.style.backgroundColor = '#e74c3c';
                feedback = 'Weak password';
            } else if (strength <= 50) {
                strengthBar.style.backgroundColor = '#f39c12';
                feedback = 'Moderate password';
            } else if (strength <= 75) {
                strengthBar.style.backgroundColor = '#3498db';
                feedback = 'Good password';
            } else {
                strengthBar.style.backgroundColor = '#2ecc71';
                feedback = 'Strong password';
            }
            
            strengthText.textContent = feedback;
        });
    }
    
    // Username validation
    const usernameInput = document.getElementById('edit_username');
    const usernameValidation = document.getElementById('username-validation');
    
    if (usernameInput && usernameValidation) {
        usernameInput.addEventListener('input', function() {
            const username = this.value.trim();
            
            if (username.length < 3) {
                usernameValidation.textContent = 'Username must be at least 3 characters';
                usernameValidation.className = 'input-validation error';
                return;
            }
            
            if (username.length > 20) {
                usernameValidation.textContent = 'Username must be less than 20 characters';
                usernameValidation.className = 'input-validation error';
                return;
            }
            
            if (!username.match(/^[a-zA-Z0-9_]+$/)) {
                usernameValidation.textContent = 'Username can only contain letters, numbers, and underscores';
                usernameValidation.className = 'input-validation error';
                return;
            }
            
            usernameValidation.textContent = 'Username is valid';
            usernameValidation.className = 'input-validation success';
        });
    }
    
    // Form validation
    const editUserForm = document.getElementById('editUserForm');
    
    if (editUserForm) {
        editUserForm.addEventListener('submit', function(e) {
            const username = usernameInput.value.trim();
            
            // Validate username
            if (username.length < 3 || username.length > 20 || !username.match(/^[a-zA-Z0-9_]+$/)) {
                e.preventDefault();
                alert('Please enter a valid username');
                usernameInput.focus();
                return;
            }
            
            // Password validation only if a password is entered
            const password = passwordInput.value;
            
            if (password.length > 0 && password.length < 6) {
                e.preventDefault();
                alert('Password must be at least 6 characters long');
                passwordInput.focus();
                return;
            }
            
            // Add loading animation to submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalContent = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
            submitBtn.disabled = true;
            
            // Re-enable button after 3 seconds if form doesn't submit for some reason
            setTimeout(() => {
                submitBtn.innerHTML = originalContent;
                submitBtn.disabled = false;
            }, 3000);
        });
    }
    
    // Add animation to the modal
    const editUserModal = document.getElementById('editUserModal');
    
    if (editUserModal) {
        editUserModal.addEventListener('animationend', function() {
            if (usernameInput) {
                usernameInput.focus();
            }
        });
    }
});
