<?php
session_start();
require_once('db_connection.php');
require_once('includes/functions.php');

// Check if user is logged in
if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Check if site is in maintenance mode
check_maintenance_mode($conn);

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Check if quiz ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: ai_quiz.php?error=No quiz specified');
    exit;
}

$quiz_id = intval($_GET['id']);

// Get quiz details
$stmt = $conn->prepare("
    SELECT q.*, m.module_name
    FROM quizzes q
    JOIN modules m ON q.module_id = m.module_id
    WHERE q.quiz_id = ? AND q.user_id = ?
");
$stmt->execute([$quiz_id, $user_id]);
$quiz = $stmt->fetch();

// Check if quiz exists
if (!$quiz) {
    header('Location: ai_quiz.php?error=Quiz not found');
    exit;
}

// Get quiz questions
$stmt = $conn->prepare("
    SELECT * FROM quiz_questions 
    WHERE quiz_id = ? 
    ORDER BY question_id ASC
");
$stmt->execute([$quiz_id]);
$questions = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes - View Quiz</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/user.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .quiz-container {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .quiz-title {
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 28px;
        }

        .quiz-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            color: var(--secondary-color);
        }

        .question-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid var(--highlight-color);
        }

        .question-text {
            font-size: 18px;
            margin-top: 0;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .options-list {
            list-style-type: none;
            padding: 0;
        }

        .option-item {
            padding: 10px 15px;
            margin-bottom: 8px;
            background-color: #fff;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .correct-option {
            background-color: rgba(40, 167, 69, 0.1);
            border-color: rgba(40, 167, 69, 0.3);
            font-weight: 500;
        }

        .answer-text {
            background-color: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            padding: 10px 15px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .quiz-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .ai-icon {
            color: var(--highlight-color);
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes</h1>
                <p class="tagline">Student Dashboard</p>
            </div>
            <div class="user-profile">
                <span class="user-name"><i class="fas fa-user-graduate"></i> <?php echo htmlspecialchars($username); ?></span>
                <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </header>

        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <div class="sidebar-menu">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        <a href="modules.php"><i class="fas fa-book"></i> Modules</a>
                        <a href="notes.php"><i class="fas fa-sticky-note"></i> Notes</a>
                        <a href="ai_quiz.php" class="active"><i class="fas fa-robot"></i> AI Quiz</a>
                        <a href="ai_summary.php"><i class="fas fa-robot"></i> AI Summary</a>
                        <a href="ai_chat.php"><i class="fas fa-robot"></i> AI Chat</a>
                        <a href="profile.php"><i class="fas fa-user"></i> Profile</a>
                    </div>
                </div>

                <div class="content">
                    <div class="page-header">
                        <h2><i class="fas fa-robot ai-icon"></i> View Quiz</h2>
                        <a href="ai_quiz.php" class="btn"><i class="fas fa-arrow-left"></i> Back to Quizzes</a>
                    </div>

                    <?php if (isset($_GET['success'])): ?>
                        <div class="success-message">
                            <?php echo htmlspecialchars($_GET['success']); ?>
                        </div>
                    <?php endif; ?>

                    <div class="quiz-container">
                        <h1 class="quiz-title"><?php echo htmlspecialchars($quiz['quiz_title']); ?></h1>

                        <div class="quiz-meta">
                            <div class="quiz-module">
                                <i class="fas fa-book"></i> <?php echo htmlspecialchars($quiz['module_name']); ?>
                            </div>
                            <div class="quiz-info">
                                <span><i class="fas fa-question-circle"></i> <?php echo count($questions); ?> questions</span>
                                <span><i class="fas fa-calendar-plus"></i> Created: <?php echo date('M d, Y', strtotime($quiz['created_at'])); ?></span>
                            </div>
                        </div>

                        <div class="questions-container">
                            <?php if (count($questions) > 0): ?>
                                <?php foreach ($questions as $index => $question): ?>
                                    <div class="question-card">
                                        <h3 class="question-text">Question <?php echo $index + 1; ?>: <?php echo htmlspecialchars($question['question_text']); ?></h3>
                                        
                                        <?php if ($question['question_type'] == 'multiple_choice' && !empty($question['options'])): ?>
                                            <?php $options = json_decode($question['options'], true); ?>
                                            <?php if (is_array($options)): ?>
                                                <ul class="options-list">
                                                    <?php foreach ($options as $letter => $option): ?>
                                                        <li class="option-item <?php echo ($letter == $question['correct_answer']) ? 'correct-option' : ''; ?>">
                                                            <strong><?php echo $letter; ?>.</strong> <?php echo htmlspecialchars($option); ?>
                                                            <?php if ($letter == $question['correct_answer']): ?>
                                                                <i class="fas fa-check" style="color: green; float: right;"></i>
                                                            <?php endif; ?>
                                                        </li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <div class="answer-text">
                                                <strong>Answer:</strong> <?php echo htmlspecialchars($question['correct_answer']); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p>No questions found for this quiz.</p>
                            <?php endif; ?>
                        </div>

                        <div class="quiz-actions">
                            <a href="take_quiz.php?id=<?php echo $quiz['quiz_id']; ?>" class="btn"><i class="fas fa-play"></i> Take Quiz</a>
                            <a href="ai_quiz.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Quizzes</a>
                            <button class="btn btn-danger" id="deleteQuizBtn" data-id="<?php echo $quiz['quiz_id']; ?>" data-title="<?php echo htmlspecialchars($quiz['quiz_title']); ?>">
                                <i class="fas fa-trash"></i> Delete Quiz
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes. All rights reserved.</p>
        </footer>
    </div>

    <!-- Delete Quiz Modal -->
    <div id="deleteQuizModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-trash"></i> Delete Quiz</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the quiz "<span id="deleteQuizTitle"></span>"?</p>
                <p>This action cannot be undone.</p>
                
                <form action="ai_quiz.php" method="post">
                    <input type="hidden" name="action" value="delete_quiz">
                    <input type="hidden" id="deleteQuizId" name="quiz_id">
                    
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary cancel-btn">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Quiz</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/user.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Delete Quiz Button
            const deleteQuizBtn = document.getElementById('deleteQuizBtn');
            const deleteQuizModal = document.getElementById('deleteQuizModal');
            const deleteQuizTitle = document.getElementById('deleteQuizTitle');
            const deleteQuizId = document.getElementById('deleteQuizId');
            
            if (deleteQuizBtn && deleteQuizModal) {
                deleteQuizBtn.addEventListener('click', function() {
                    const quizId = this.getAttribute('data-id');
                    const quizTitle = this.getAttribute('data-title');
                    
                    if (deleteQuizTitle) deleteQuizTitle.textContent = quizTitle;
                    if (deleteQuizId) deleteQuizId.value = quizId;
                    
                    openModalWithAnimation(deleteQuizModal);
                });
            }
        });
    </script>
</body>
</html>
