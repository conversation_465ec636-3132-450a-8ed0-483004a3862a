/**
 * Quiz functionality for StudyNotes
 */
document.addEventListener('DOMContentLoaded', function() {
    // Handle expandable question content
    const questionTexts = document.querySelectorAll('.question-text');

    questionTexts.forEach(questionText => {
        const questionId = questionText.id.replace('questionText_', '');
        const expandCollapseBtn = document.getElementById('expandCollapseBtn_' + questionId);

        // Check if the content is long enough to need expansion
        if (questionText && questionText.scrollHeight > 200 && expandCollapseBtn) {
            // Show the expand/collapse button
            expandCollapseBtn.style.display = 'block';
        }
    });

    // Add click event to toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-question-btn');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const questionId = this.getAttribute('data-question-id');
            const questionText = document.getElementById('questionText_' + questionId);
            const buttonText = this.querySelector('span');
            const buttonIcon = this.querySelector('i');

            if (questionText.classList.contains('expanded')) {
                // Collapse
                questionText.classList.remove('expanded');
                buttonText.textContent = 'Show More';
                buttonIcon.className = 'fas fa-chevron-down';

                // Scroll back to the top of the content
                questionText.scrollTop = 0;
            } else {
                // Expand
                questionText.classList.add('expanded');
                buttonText.textContent = 'Show Less';
                buttonIcon.className = 'fas fa-chevron-up';
            }
        });
    });

    // Add click event to option items to improve UX
    const optionItems = document.querySelectorAll('.option-item');
    optionItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Don't trigger if clicking directly on the radio button (it will handle itself)
            if (e.target.type !== 'radio') {
                // Find the radio button inside this option item and click it
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
            }
        });
    });
    // Setup generate quiz modal
    const generateQuizBtn = document.getElementById('generateQuizBtn');
    const generateQuizModal = document.getElementById('generateQuizModal');
    const generateQuizForm = document.getElementById('generateQuizForm');
    const noteSelect = document.getElementById('note_id');
    const moduleIdInput = document.getElementById('module_id');
    const generationProgress = document.getElementById('generationProgress');

    // Setup delete quiz modal
    const deleteQuizBtns = document.querySelectorAll('.delete-quiz-btn');
    const deleteQuizModal = document.getElementById('deleteQuizModal');

    // Open generate quiz modal
    if (generateQuizBtn && generateQuizModal) {
        generateQuizBtn.addEventListener('click', function() {
            openModal(generateQuizModal);
        });
    }

    // Handle note selection to set module_id
    if (noteSelect && moduleIdInput) {
        noteSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const moduleId = selectedOption.getAttribute('data-module');
            moduleIdInput.value = moduleId || 0;

            // Auto-fill quiz title based on note title
            const quizTitleInput = document.getElementById('quiz_title');
            if (quizTitleInput) {
                const noteTitle = selectedOption.textContent.split('(')[0].trim();
                quizTitleInput.value = `Quiz on ${noteTitle}`;
            }
        });
    }

    // Handle quiz generation form submission
    if (generateQuizForm) {
        generateQuizForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show progress indicator
            if (generationProgress) {
                generateQuizForm.style.display = 'none';
                generationProgress.style.display = 'block';
            }

            // Get form data
            const formData = {
                note_id: document.getElementById('note_id').value,
                quiz_title: document.getElementById('quiz_title').value,
                num_questions: document.getElementById('num_questions').value,
                difficulty: document.querySelector('input[name="difficulty"]:checked').value,
                module_id: document.getElementById('module_id').value
            };

            // Send API request
            fetch('api/generate_quiz.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect to take quiz page
                    window.location.href = `take_quiz.php?id=${data.quiz_id}&success=Quiz generated successfully`;
                } else {
                    // Show error
                    alert('Error: ' + (data.error || 'Failed to generate quiz'));

                    // Hide progress indicator
                    if (generationProgress) {
                        generateQuizForm.style.display = 'block';
                        generationProgress.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while generating the quiz. Please try again.');

                // Hide progress indicator
                if (generationProgress) {
                    generateQuizForm.style.display = 'block';
                    generationProgress.style.display = 'none';
                }
            });
        });
    }

    // Setup delete quiz buttons
    if (deleteQuizBtns.length > 0 && deleteQuizModal) {
        deleteQuizBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const quizId = this.getAttribute('data-id');
                const quizTitle = this.getAttribute('data-title');

                document.getElementById('delete_quiz_id').value = quizId;
                document.getElementById('delete_quiz_title').textContent = quizTitle;

                openModal(deleteQuizModal);
            });
        });
    }

    // Setup quiz form validation
    if (generateQuizForm) {
        const numQuestionsInput = document.getElementById('num_questions');

        if (numQuestionsInput) {
            numQuestionsInput.addEventListener('input', function() {
                const value = parseInt(this.value);
                if (value < 1) this.value = 1;
                if (value > 20) this.value = 20;
            });
        }
    }

    // Close modals
    const closeBtns = document.querySelectorAll('.close, .cancel-btn');
    closeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                closeModal(modal);

                // Reset form if it's the generate quiz modal
                if (modal.id === 'generateQuizModal' && generateQuizForm && generationProgress) {
                    generateQuizForm.style.display = 'block';
                    generationProgress.style.display = 'none';
                }
            }
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);

            // Reset form if it's the generate quiz modal
            if (e.target.id === 'generateQuizModal' && generateQuizForm && generationProgress) {
                generateQuizForm.style.display = 'block';
                generationProgress.style.display = 'none';
            }
        }
    });
});
