/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:root {
    --primary-color: #65350F;       /* <PERSON> Brown */
    --secondary-color: #A67B5B;     /* Medium Brown */
    --accent-color: #D4A373;        /* Light Brown */
    --highlight-color: #E8871E;     /* Orange */
    --text-color: #2D2424;          /* Dark Brown Text */
    --light-text: #F5EBE0;          /* Light Text */
    --background-color: #F5EBE0;    /* Light Beige */
    --card-color: #FFFFFF;          /* White */
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 1.5rem 3rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.logo h1 {
    font-size: 2.2rem;
    font-weight: bold;
    letter-spacing: 1px;
}

.tagline {
    font-style: italic;
    opacity: 0.9;
    margin-top: -5px;
}

/* Main Content Styles */
main {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.welcome-section {
    text-align: center;
    margin-bottom: 3rem;
}

.welcome-section h2 {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.welcome-section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--highlight-color);
}

.welcome-section p {
    max-width: 800px;
    margin: 0 auto 2rem;
}

/* Features Section */
.features {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
}

.feature-card {
    background-color: var(--card-color);
    border-radius: 10px;
    padding: 2rem;
    width: 250px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    border-top: 5px solid var(--accent-color);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--highlight-color);
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.feature-card p {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Access Section */
.access-section {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.access-card {
    background-color: var(--card-color);
    border-radius: 10px;
    padding: 2rem;
    width: 300px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
    border-bottom: 5px solid var(--highlight-color);
}

.access-card:hover {
    transform: translateY(-5px);
}

.access-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.access-card p {
    margin-bottom: 1.5rem;
    color: var(--secondary-color);
}

.access-card.admin {
    border-bottom-color: var(--primary-color);
}

.btn {
    display: inline-block;
    padding: 0.8rem 2rem;
    background-color: var(--highlight-color);
    color: var(--light-text);
    text-decoration: none;
    border-radius: 50px;
    font-weight: bold;
    transition: all 0.3s;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.admin-btn {
    background-color: var(--primary-color);
}

.admin-btn:hover {
    background-color: var(--secondary-color);
}

/* Footer Styles */
footer {
    background-color: var(--primary-color);
    color: var(--light-text);
    text-align: center;
    padding: 1.5rem;
    margin-top: auto;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .features {
        gap: 1rem;
    }
    
    .feature-card {
        width: 100%;
        max-width: 300px;
    }
    
    .access-card {
        width: 100%;
        max-width: 300px;
    }
}