/**
 * Enhanced Admin Dashboard JavaScript
 * With improved animations, interactivity, and visual effects
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all dashboard components
    initDashboard();

    // Setup enhanced modal functionality
    setupModals();

    // Configure data tables with sorting and filtering
    setupDataTables();

    // Add animation effects to dashboard elements
    animateDashboardElements();

    // Setup notification system
    setupNotifications();

    // Setup interactive charts if needed
    setupCharts();

    // Add motion effects to actions
    setupActionEffects();

    // Setup auto-save for forms
    setupFormAutoSave();

    // Add keyboard shortcuts for power users
    setupKeyboardShortcuts();

    // Add dashboard easter egg
    console.log('%c Welcome to StudyNotes Admin! ', 'background: linear-gradient(to right, #65350F, #A67B5B); color: white; font-size: 1.2rem; padding: 5px; border-radius: 5px;');

    // Konami code easter egg (up, up, down, down, left, right, left, right, b, a)
    let konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', '<PERSON>Left', 'ArrowRight', 'KeyB', 'KeyA'];
    let konamiPosition = 0;

    document.addEventListener('keydown', function(e) {
        // Check if the key matches the next key in the Konami code
        if (e.code === konamiCode[konamiPosition]) {
            konamiPosition++;

            // If the entire sequence is matched
            if (konamiPosition === konamiCode.length) {
                activateEasterEgg();
                konamiPosition = 0;
            }
        } else {
            konamiPosition = 0;
        }
    });

    function activateEasterEgg() {
        // Create matrix-like animation
        const body = document.body;
        const canvas = document.createElement('canvas');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        canvas.style.position = 'fixed';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.zIndex = '9999';
        canvas.style.pointerEvents = 'none';

        body.appendChild(canvas);

        const ctx = canvas.getContext('2d');

        // Matrix-like characters
        const characters = '01'.split('');
        const fontSize = 14;
        const columns = canvas.width / fontSize;

        // Array to track the y position of each column
        const drops = [];
        for (let i = 0; i < columns; i++) {
            drops[i] = 1;
        }

        // Draw the animation
        function draw() {
            // Add semi-transparent black rectangle to create fade effect
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Set color and font
            ctx.fillStyle = '#65350F';
            ctx.font = fontSize + 'px monospace';

            // Loop through each column
            for (let i = 0; i < drops.length; i++) {
                // Choose a random character
                const text = characters[Math.floor(Math.random() * characters.length)];

                // Draw the character
                ctx.fillText(text, i * fontSize, drops[i] * fontSize);

                // Move the drop down when it reaches the bottom or at random
                if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                    drops[i] = 0;
                }

                // Increment the y coordinate
                drops[i]++;
            }
        }

        // Matrix animation interval
        const matrixInterval = setInterval(draw, 33);

        // Create floating admin logo
        const logo = document.createElement('div');
        logo.textContent = 'StudyNotes Admin';
        logo.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-family: monospace;
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 0 0 10px var(--primary-color);
            z-index: 10000;
            animation: pulsate 2s ease-in-out infinite;
            pointer-events: none;
            letter-spacing: 2px;
        `;

        // Add pulsating animation
        const style = document.createElement('style');
        style.innerHTML = `
            @keyframes pulsate {
                0% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.1); }
                100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            }
        `;
        document.head.appendChild(style);
        body.appendChild(logo);

        // Remove everything after 5 seconds
        setTimeout(() => {
            clearInterval(matrixInterval);
            canvas.remove();
            logo.remove();
        }, 5000);
    }
});

/**
 * Sort table function for data tables
 */
function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    // Get sort direction
    const th = table.querySelectorAll('th')[columnIndex];
    const sortDirection = th.getAttribute('data-sort') === 'asc' ? 'desc' : 'asc';

    // Sort the rows
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();

        // Check if values are numbers
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return sortDirection === 'asc' ? aNum - bNum : bNum - aNum;
        } else {
            return sortDirection === 'asc' ?
                aValue.localeCompare(bValue) :
                bValue.localeCompare(aValue);
        }
    });

    // Animate the sorting
    rows.forEach(row => {
        row.style.transition = 'background-color 0.3s';
        row.style.backgroundColor = 'rgba(232, 135, 30, 0.1)';
    });

    // Remove all rows
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }

    // Append sorted rows
    rows.forEach((row, index) => {
        // Add a small delay for each row for a wave effect
        setTimeout(() => {
            tbody.appendChild(row);
            setTimeout(() => {
                row.style.backgroundColor = 'transparent';
            }, 300);
        }, index * 50);
    });
}

/**
 * Initialize dashboard components
 */
function initDashboard() {
    // Initialize sidebar menu with active state
    const currentPath = window.location.pathname;
    console.log('Current path:', currentPath);
    const sidebarLinks = document.querySelectorAll('.sidebar-menu a');

    // First, remove all active classes
    sidebarLinks.forEach(link => {
        link.classList.remove('active');
    });

    // Then set the active class based on the current page
    const currentFile = currentPath.split('/').pop();
    console.log('Current file:', currentFile);

    // Add click event listeners to sidebar links
    sidebarLinks.forEach(link => {
        const href = link.getAttribute('href');
        const linkFile = href.split('/').pop();
        console.log('Link file:', linkFile);

        // Check if this link should be active
        if (currentFile === linkFile) {
            link.classList.add('active');

            // Add animated highlight effect to active menu item
            link.style.position = 'relative';

            const activeHighlight = document.createElement('span');
            activeHighlight.style.cssText = `
                position: absolute;
                left: 0;
                top: 0;
                width: 0;
                height: 100%;
                background-color: rgba(101, 53, 15, 0.1);
                z-index: -1;
                border-radius: 0 4px 4px 0;
                transition: width 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            `;

            link.appendChild(activeHighlight);

            setTimeout(() => {
                activeHighlight.style.width = '100%';
            }, 300);
        }

        // Add click event to handle navigation
        link.addEventListener('click', function() {
            // Don't prevent default - let the browser navigate
        });

        // Add hover animation
        link.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.paddingLeft = '2rem';
                this.style.backgroundColor = 'rgba(101, 53, 15, 0.03)';
            }
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.paddingLeft = '1.5rem';
                this.style.backgroundColor = 'transparent';
            }
        });
    });

    // Add collapsible sidebar functionality for mobile
    const sidebarToggle = document.createElement('button');
    sidebarToggle.classList.add('sidebar-toggle');
    sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
    sidebarToggle.style.cssText = `
        display: none;
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        z-index: 100;
        transition: transform 0.3s;
    `;

    document.body.appendChild(sidebarToggle);

    const sidebar = document.querySelector('.sidebar');

    // Show toggle button on mobile
    if (window.innerWidth <= 992) {
        sidebarToggle.style.display = 'flex';
        sidebarToggle.style.alignItems = 'center';
        sidebarToggle.style.justifyContent = 'center';

        // Initially hide sidebar on mobile
        if (sidebar) {
            sidebar.style.transform = 'translateX(-110%)';
            sidebar.style.position = 'fixed';
            sidebar.style.top = '0';
            sidebar.style.left = '0';
            sidebar.style.height = '100vh';
            sidebar.style.zIndex = '99';
            sidebar.style.transition = 'transform 0.3s cubic-bezier(0.65, 0, 0.35, 1)';
        }
    }

    sidebarToggle.addEventListener('click', function() {
        if (sidebar) {
            if (sidebar.style.transform === 'translateX(-110%)') {
                sidebar.style.transform = 'translateX(0)';
                this.innerHTML = '<i class="fas fa-times"></i>';
                this.style.transform = 'rotate(90deg)';
            } else {
                sidebar.style.transform = 'translateX(-110%)';
                this.innerHTML = '<i class="fas fa-bars"></i>';
                this.style.transform = 'rotate(0)';
            }
        }
    });

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 992 && sidebar &&
            !sidebar.contains(e.target) &&
            e.target !== sidebarToggle &&
            !sidebarToggle.contains(e.target)) {
            sidebar.style.transform = 'translateX(-110%)';
            sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
            sidebarToggle.style.transform = 'rotate(0)';
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 992) {
            sidebarToggle.style.display = 'flex';

            if (sidebar) {
                sidebar.style.position = 'fixed';
                sidebar.style.transition = 'transform 0.3s cubic-bezier(0.65, 0, 0.35, 1)';
            }
        } else {
            sidebarToggle.style.display = 'none';

            if (sidebar) {
                sidebar.style.transform = 'translateX(0)';
                sidebar.style.position = 'static';
                sidebar.style.height = 'auto';
            }
        }
    });
}

/**
 * Setup enhanced modal functionality
 */
function setupModals() {
    // Get all modals
    const addUserModal = document.getElementById('addUserModal');
    const editUserModal = document.getElementById('editUserModal');
    const deleteUserModal = document.getElementById('deleteUserModal');

    // Get all modal triggers
    const addUserBtn = document.getElementById('addUserBtn');
    const editBtns = document.querySelectorAll('.edit-btn');
    const deleteBtns = document.querySelectorAll('.delete-btn');

    // Get all close buttons
    const closeBtns = document.querySelectorAll('.close');
    const cancelBtns = document.querySelectorAll('.cancel-btn');

    // Store all modals in array for easier access
    const modals = [addUserModal, editUserModal, deleteUserModal].filter(modal => modal);

    // Add User Button Click with enhanced animation
    if (addUserBtn) {
        addUserBtn.addEventListener('click', function() {
            if (addUserModal) {
                // Show modal with advanced animation
                openModalWithAnimation(addUserModal);
            }
        });
    }

    // Edit User Button Click with data loading animation
    editBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');

            if (editUserModal) {
                // Show loading animation
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                btn.disabled = true;

                // Simulate data loading (in real app, this would be an API call)
                setTimeout(() => {
                    document.getElementById('edit_user_id').value = userId;
                    document.getElementById('edit_username').value = username;
                    document.getElementById('edit_password').value = ''; // Clear password field

                    // Reset button and open modal
                    btn.innerHTML = '<i class="fas fa-edit"></i>';
                    btn.disabled = false;

                    openModalWithAnimation(editUserModal);
                }, 500);
            }
        });
    });

    // Delete User Button Click with warning animation
    deleteBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');

            if (deleteUserModal) {
                document.getElementById('delete_user_id').value = userId;

                // Enhanced warning display
                const usernameEl = document.getElementById('delete_username');
                if (usernameEl) {
                    usernameEl.innerHTML = `<span style="color: #e74c3c; font-weight: bold;">${username}</span>`;

                    // Add warning shake animation
                    usernameEl.style.animation = 'shake 0.5s';

                    // Add animation to make modal appear with warning style
                    deleteUserModal.classList.add('warning-modal');

                    // Define the shake animation if not already in CSS
                    if (!document.querySelector('style#shake-animation')) {
                        const style = document.createElement('style');
                        style.id = 'shake-animation';
                        style.innerHTML = `
                            @keyframes shake {
                                0%, 100% { transform: translateX(0); }
                                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                                20%, 40%, 60%, 80% { transform: translateX(5px); }
                            }
                        `;
                        document.head.appendChild(style);
                    }
                }

                openModalWithAnimation(deleteUserModal);
            }
        });
    });

    // Close Button Click with fade out animation
    closeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                closeModalWithAnimation(modal);
            }
        });
    });

    // Cancel Button Click with fade out animation
    cancelBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                closeModalWithAnimation(modal);
            }
        });
    });

    // Close modal when clicking outside with fade out animation
    window.addEventListener('click', function(event) {
        modals.forEach(modal => {
            if (event.target === modal) {
                closeModalWithAnimation(modal);
            }
        });
    });

    // Enhanced modal opening animation
    function openModalWithAnimation(modal) {
        // Reset any existing animation classes
        modal.classList.remove('modal-closing');

        // Set the modal display to block first
        modal.style.display = 'block';

        // Get the modal content for animation
        const modalContent = modal.querySelector('.modal-content');

        if (modalContent) {
            // Reset any existing animations
            modalContent.style.animation = 'none';

            // Trigger reflow to restart animation
            void modalContent.offsetWidth;

            // Apply enhanced animation
            modalContent.style.animation = 'modalFadeIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards';

            // Add blur effect to background
            document.querySelector('main').style.filter = 'blur(2px)';
            document.querySelector('main').style.transition = 'filter 0.3s';
        }
    }

    // Enhanced modal closing animation
    function closeModalWithAnimation(modal) {
        // Add closing class to modal
        modal.classList.add('modal-closing');

        // Get the modal content
        const modalContent = modal.querySelector('.modal-content');

        if (modalContent) {
            // Apply closing animation
            modalContent.style.animation = 'modalFadeOut 0.3s forwards';

            // Remove blur effect from background
            document.querySelector('main').style.filter = 'blur(0)';

            // Define the fade out animation if not already in CSS
            if (!document.querySelector('style#modal-animations')) {
                const style = document.createElement('style');
                style.id = 'modal-animations';
                style.innerHTML = `
                    @keyframes modalFadeOut {
                        from {opacity: 1; transform: translateY(0);}
                        to {opacity: 0; transform: translateY(-20px);}
                    }
                `;
                document.head.appendChild(style);
            }

            // Wait for animation to complete before hiding modal
            setTimeout(() => {
                modal.style.display = 'none';
                modal.classList.remove('warning-modal', 'modal-closing');
            }, 300);
        } else {
            // Fallback if modalContent not found
            modal.style.display = 'none';
        }
    }
}

/**
 * Setup data tables with sorting and filtering
 */
function setupDataTables() {
    const tables = document.querySelectorAll('.users-table');

    tables.forEach(table => {
        // Add sorting functionality to table headers
        const headers = table.querySelectorAll('th');

        headers.forEach(header => {
            if (header.getAttribute('data-sortable') !== 'false') {
                header.style.cursor = 'pointer';
                header.title = 'Click to sort';

                // Add sort indicator
                const sortIndicator = document.createElement('span');
                sortIndicator.classList.add('sort-indicator');
                sortIndicator.innerHTML = ' <i class="fas fa-sort"></i>';
                header.appendChild(sortIndicator);

                // Add click event
                header.addEventListener('click', function() {
                    const columnIndex = Array.from(headers).indexOf(this);
                    sortTable(table, columnIndex);

                    // Update sort indicators
                    headers.forEach(h => {
                        const indicator = h.querySelector('.sort-indicator');
                        if (indicator) {
                            indicator.innerHTML = ' <i class="fas fa-sort"></i>';
                        }
                    });

                    // Set active sort indicator
                    const sortOrder = this.getAttribute('data-sort') === 'asc' ? 'desc' : 'asc';
                    this.setAttribute('data-sort', sortOrder);

                    const thisIndicator = this.querySelector('.sort-indicator');
                    if (thisIndicator) {
                        thisIndicator.innerHTML = sortOrder === 'asc' ?
                            ' <i class="fas fa-sort-up"></i>' :
                            ' <i class="fas fa-sort-down"></i>';
                    }
                });
            }
        });
    });
}

