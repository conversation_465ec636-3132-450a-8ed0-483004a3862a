/* Summary Styles */

/* Summary-specific grid and card extensions */
.summaries-grid {
    /* Inherits from .grid-container in common.css */
}

.summary-preview {
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 15px;
    flex-grow: 1;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

/* Uses .card-footer from common.css */

.summary-date {
    color: var(--secondary-color);
    font-size: 13px;
}

.summary-date i {
    margin-right: 5px;
    color: var(--highlight-color);
}

.summary-actions {
    display: flex;
    gap: 5px;
}

/* Styles moved to common.css - Empty State, Generation Progress, Spinner */

/* Filter Badge - uses common.css styles */

/* Message styles moved to common.css - Warning, Success, Error Messages */
