/* Summary Styles */

/* Summary Card */
.summaries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.summary-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    flex-direction: column;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.summary-header {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.summary-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
    flex: 1;
}

.module-badge {
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    margin-left: 10px;
}

.summary-preview {
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 15px;
    flex-grow: 1;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

.summary-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 15px;
}

.summary-date {
    color: var(--secondary-color);
    font-size: 13px;
}

.summary-date i {
    margin-right: 5px;
    color: var(--highlight-color);
}

.summary-actions {
    display: flex;
    gap: 5px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--accent-color);
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

/* Generation Progress */
.generation-progress {
    text-align: center;
    padding: 30px 20px;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--highlight-color);
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.generation-progress p {
    margin-bottom: 5px;
    color: var(--primary-color);
    font-weight: 500;
}

.generation-progress p.small {
    font-size: 14px;
    color: var(--secondary-color);
}

/* Filter Badge */
.filter-badge {
    display: inline-flex;
    align-items: center;
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 10px;
}

.filter-badge a {
    color: var(--primary-color);
    margin-left: 5px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
}

.filter-badge a:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

/* Warning Message */
.warning {
    color: #856404;
    background-color: #fff3cd;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
    border-left: 4px solid #ffc107;
}

/* Success and Error Messages */
.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border-left: 4px solid #28a745;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border-left: 4px solid #dc3545;
}

/* Slim buttons - Using standardized style from style.css */
